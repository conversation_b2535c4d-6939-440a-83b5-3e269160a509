import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { ListRoleResource, SPECIAL_CHARACTER_PATTERN, SpecialCharacterErrorMessage, Small_TextBoxMaxCharactersAllowedMessage, Small_TextBoxMaxLength } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { RoleFilterAction } from 'src/app/model/Role/roleFilterAction.model';
import { RolePermissionResponse } from 'src/app/model/Role/rolePermissionResponse.model';
import { RoleRequestBody } from 'src/app/model/Role/roleRequestBody.model';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { RoleService } from 'src/app/shared/Service/RoleService/role.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { isUndefined } from 'is-what';

/**
 * <AUTHOR>
 */
@Component({
  selector: 'app-rolefilter',
  templateUrl: './rolefilter.component.html',
  styleUrls: ['./rolefilter.component.css']
})
export class RolefilterComponent implements OnInit {

  @Input("isFilterComponentInitWithApicall") isFilterComponentInitWithApicall: boolean;
  @Input("listPageRefreshForbackToDetailPage") listPageRefreshForbackToDetailPage: boolean;
  @Input("roleSearchRequestBody") roleSearchRequestBody: RoleRequestBody;

  permissionSetting: MultiSelectDropdownSettings = null;

  disabled = false;

  permissionList: RolePermissionResponse[] = [];

  //MaxLength Message
  textBoxMaxLengthMessage: string = Small_TextBoxMaxCharactersAllowedMessage;
  specialCharacterErrorMessage: string = SpecialCharacterErrorMessage;


  filterRoleForm = new FormGroup({
    roleName: new FormControl('', [Validators.maxLength(Small_TextBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    permission: new FormControl([], [])
  });

  subscriptionForRefeshList: Subscription;

  defaultListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

  constructor(private roleService: RoleService,
    private roleApiCallService: RoleApiCallService,
    private commonsService: CommonsService,
    private commonOperationsService: CommonOperationsService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService) { }

  public ngOnInit(): void {
    this.permissionSetting = this.multiSelectDropDownSettingService.getPermissionDrpSetting(false);
    this.onInitSubject();
    // Only call API if no cached data is available
    this.getRolePermissionList();
    if (this.isFilterComponentInitWithApicall) {
      this.clearFilter(this.defaultListingPageReloadSubjectParameter);
    }
  }

  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForRefeshList)) { this.subscriptionForRefeshList.unsubscribe() }
  }

  public onInitSubject(): void {
    /**
     * Role Detail Page Refresh After some Action Like Create or Update or Delete Role
     * <AUTHOR>
     */
    this.subscriptionForRefeshList = this.roleService.getRoleListRefreshSubject().subscribe((listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
      if (listingPageReloadSubjectParameter.isReloadData) {
        if (listingPageReloadSubjectParameter.isClearFilter) {
          this.clearFilter(listingPageReloadSubjectParameter);
        } else {
          //page change 1,2,3
          this.roleListPageRefresh(listingPageReloadSubjectParameter);
        }
      }
    });
  }

  public async getRolePermissionList(): Promise<void> {
    if (this.roleService.getRolePermissionList().length === 0) {
      this.permissionList = await this.roleApiCallService.getRolePermissionList(ListRoleResource);
      this.roleService.setRolePermissionList(this.permissionList)
    } else {
      this.permissionList = this.roleService.getRolePermissionList();
    }

    this.setFilterValue();
  }

  /**
   * Set Old Filter Value
   * 
   * <AUTHOR>
   */
  private setFilterValue() {
    if (this.roleSearchRequestBody != null) {
      this.filterRoleForm.get('roleName').setValue(this.roleSearchRequestBody.name);
      this.filterRoleForm.get('permission').setValue(this.roleSearchRequestBody.permissions);
    }
    if (this.listPageRefreshForbackToDetailPage) {
      this.roleListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
   * Reload Listing Data
   * <AUTHOR>
   */
  public searchData(): void {
    let allFormValue = this.filterRoleForm.value;
    this.filterRoleForm.get('roleName').setValue(this.commonsService.checkNullFieldValue(allFormValue.roleName));
    if (this.commonsService.checkValueIsNullOrEmpty(allFormValue.roleName) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.permission)) {
      this.commonOperationsService.showEmptyFilterTosteMessge();
    } else {
      this.roleListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
   * Clear All filter and Reload Data
   * <AUTHOR>
   */
  public clearFilter(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    this.clearAllFilter();
    this.roleListPageRefresh(listingPageReloadSubjectParameter);
  }

  /**
   * Clear All filter
   * <AUTHOR>
   */
  public clearAllFilter(): void {
    this.filterRoleForm.get('roleName').setValue("");
    this.filterRoleForm.get('permission').setValue([]);
  }

  /**
   * Get Filter Data and pass to Listing page and Reload Page 
   * <AUTHOR>
   */
  private roleListPageRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    if (this.filterRoleForm.invalid) {
      this.filterRoleForm.reset();
    }
    let allFormValue = this.filterRoleForm.value;
    let permissionId = allFormValue.permission;
    let roleRequestBody = new RoleRequestBody(this.commonsService.checkNullFieldValue(allFormValue.roleName), permissionId);
    let roleFilterAction = new RoleFilterAction(listingPageReloadSubjectParameter, roleRequestBody);
    this.roleService.callRoleListFilterRequestParameterSubject(roleFilterAction);
  }

}
