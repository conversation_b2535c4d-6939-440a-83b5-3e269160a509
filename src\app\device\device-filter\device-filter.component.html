<!-- Device Filter Component -->
<div class="col-md-12 p-0" id="deviceFilter">
    <!-- filter header start -->
    <label class="col-md-12 h5-tag">Filter</label>
    <!-- filter header end -->
    <div class="card mt-3">
        <!-- filter card body start -->
        <div class="card-body">
            <!-- device filter form start -->
            <form id="filter-form" [formGroup]="filterForm" role="form" class="form">

                <!-- deviceSerialNo id field start -->
                <div class="form-group">
                    <label class="form-control-label" for="field_deviceSerialNo"><strong>Serial No</strong></label>
                    <!-- deviceSerialNo input start -->
                    <input class="form-control" type="text" formControlName="deviceSerialNo" />
                    <!-- deviceSerialNo input start -->
                    <!-------------------------------------->
                    <div
                        *ngIf="(filterForm.get('deviceSerialNo').touched || filterForm.get('deviceSerialNo').dirty) && filterForm.get('deviceSerialNo').invalid ">
                        <div *ngIf="filterForm.controls['deviceSerialNo'].hasError('maxlength')">
                            <span class="alert-color font-12">{{small_textBoxMaxCharactersAllowedMessage}}</span>
                        </div>
                        <div *ngIf="filterForm.controls['deviceSerialNo'].hasError('pattern')">
                            <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
                        </div>
                    </div>
                    <!-------------------------------------->
                </div>
                <!-- deviceSerialNofield end -->

                <!-- hardware id field start -->
                <div class="form-group">
                    <label class="form-control-label" for="field_deviceType"><strong>HW ID</strong></label>
                    <!-- hardware id input start -->
                    <input class="form-control" type="text" formControlName="deviceId" />
                    <!-- hardware id input start -->
                    <!-------------------------------------->
                    <div
                        *ngIf="(filterForm.get('deviceId').touched || filterForm.get('deviceId').dirty) && filterForm.get('deviceId').invalid ">
                        <div *ngIf="filterForm.controls['deviceId'].hasError('maxlength')">
                            <span class="alert-color font-12">{{small_textBoxMaxCharactersAllowedMessage}}</span>
                        </div>
                        <div *ngIf="filterForm.controls['deviceId'].hasError('pattern')">
                            <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
                        </div>
                    </div>
                    <!-------------------------------------->
                </div>
                <!-- hardware id field end -->

                <!---------------------------->
                <!--Sales order number start-->
                <div class="form-group">
                    <label class="form-control-label" for="field_sales_order_number"><strong>Sales Order
                            Numbers</strong></label>
                    <ng-multiselect-dropdown name="salesOrderNumber" [placeholder]="''"
                        [settings]="salesOrderNumberSetting" [data]="salesOrderNumberList"
                        formControlName="salesOrderNumber">
                    </ng-multiselect-dropdown>
                </div>
                <!--Sales order number end-->
                <!---------------------------->

                <!-- device type start -->
                <div class="form-group">
                    <label class="form-control-label" for="field_drpDeviceType"><strong>Device Type</strong></label>
                    <ng-multiselect-dropdown name="drpDeviceType" class="devicePageDeviceType"
                        formControlName="drpDeviceType" [settings]="dropdownSettingsDeviceType" [data]="deviceTypes">
                    </ng-multiselect-dropdown>
                </div>
                <!-- device type end -->

                <!-- countries filter start -->
                <div class="form-group">
                    <label class="form-control-label" for="field_countries"><strong>Country</strong></label>
                    <ng-multiselect-dropdown name="countries" [placeholder]="''" formControlName="countries"
                        id="deviceCountry" [settings]="countrySetting" [data]="countryList"
                        (onSelect)="onCountrySelect($event)" (onDeSelect)="onCountryDeSelect()">
                    </ng-multiselect-dropdown>
                </div>
                <!-- countries filter end -->

                <!-- system software version start -->
                <div class="form-group">
                    <label class="form-control-label" for="field_packageVersions"><strong>System SW
                            Version</strong></label>
                    <!-- system software version multiselect dropdown start -->
                    <ng-multiselect-dropdown name="packageVersions" [placeholder]="''" formControlName="packageVersions"
                        [settings]="systemSoftwearVersionDropdownSetting" [data]="packageVersionsList">
                    </ng-multiselect-dropdown>
                    <!-- system software version multiselect dropdown end -->
                </div>
                <!-- system software version end -->

                <!-- Connection status field start -->
                <div class="form-group">
                    <label class="form-control-label" for="field_connectionState"><strong>Connection
                            State</strong></label>
                    <!-- Connection status multiselect dropdown start -->
                    <ng-multiselect-dropdown name="connectionState" class="devicePageDeviceType" [placeholder]="''"
                        formControlName="connectionState" [settings]="deviceConnectionStateDropdownSetting"
                        [data]="deviceConnectionState">
                    </ng-multiselect-dropdown>
                    <!-- Connection status multiselect dropdown end -->
                </div>
                <!-- Connection status field end -->

                <!-------------------------------------->
                <!-------------------------------------->
                <!-- Customer Name field start -->
                <!-------------------------------------->
                <!-------------------------------------->
                <div class="form-group">
                    <label class="form-control-label" for="field_customerName"><strong>Customer
                            Name</strong></label>
                    <input class="form-control" type="text" formControlName="customerName" />
                    <div
                        *ngIf="(filterForm.get('customerName').touched || filterForm.get('customerName').dirty) && 
                                                                                                        filterForm.get('customerName').invalid ">
                        <div *ngIf="filterForm.controls['customerName'].hasError('maxlength')">
                            <span class="alert-color font-12">{{textBoxMaxCharactersAllowedMessage}}</span>
                        </div>
                        <div *ngIf="filterForm.controls['customerName'].hasError('pattern')">
                            <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
                        </div>
                    </div>
                </div>
                <!-------------------------------------->
                <!-------------------------------------->
                <!-- Customer Name field end -->
                <!-------------------------------------->
                <!-------------------------------------->

                <!-- device lock status start -->
                <div class="form-group">
                    <label class="form-control-label" for="field_deviceLockState"><strong>
                            Locked</strong></label>
                    <ng-multiselect-dropdown name="deviceLockState" class="devicePageDeviceType" [placeholder]="''"
                        formControlName="deviceLockState" [settings]="dropdownSettingsForLockState"
                        [data]="lockUnlockStateList">
                    </ng-multiselect-dropdown>
                </div>
                <!-- device lock status end -->

                <!-- device Edit status start -->
                <div class="form-group">
                    <label class="form-control-label" for="field_deviceEditState"><strong>
                            Editable</strong></label>
                    <ng-multiselect-dropdown name="deviceEditState" class="multiselectdropdown" [placeholder]="''"
                        formControlName="deviceEditState" [settings]="dropdownSettingsForEditState"
                        [data]="editStateList">
                    </ng-multiselect-dropdown>
                </div>
                <!-- device Edit status end -->

                <!-------------------------------------------->
                <!-------------------------------------------->
                <!-- Device productStatus form group start -->
                <div class="form-group">
                    <label class="form-control-label" for="device_field_productStatus"
                        id="label_productStatus_device"><strong> Status</strong></label>
                    <!-- Device Enabled / Disabled selection start -->
                    <ng-multiselect-dropdown id="device_field_productStatus" name="productStatus" [placeholder]="''"
                        formControlName="productStatus" [settings]="dropdownSettingsForProductStatus"
                        [data]="productStatusList">
                    </ng-multiselect-dropdown>
                    <!-- Device productStatus selection end -->
                </div>
                <!-- Device productStatus form group end -->
                <!-------------------------------------------->
                <!-------------------------------------------->

                <hr class="mt-1 mb-2">
                <div class="">
                    <!-- Search button -->
                    <button class="btn btn-sm btn-orange mr-3" (click)="searchData()" [disabled]="filterForm.invalid"
                        id="searchDevice">Search</button>
                    <!-- Clear button -->
                    <button class="btn btn-sm btn-orange"
                        (click)="clearFilter(defaultListingPageReloadSubjectParameter)">Clear</button>
                </div>
            </form>
            <!-- device filter form end -->
        </div>
        <!-- filter card body end -->
    </div>
</div>