import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, OnInit, Input, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { SPECIAL_CHARACTER_PATTERN, SpecialCharacterErrorMessage, Small_TextBoxMaxCharactersAllowedMessage, Small_TextBoxMaxLength, TextAreaMaxCharactersAllowedMessage, TextAreaMaxLength } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { CreateAndUpdateRoleModelRequest } from 'src/app/model/Role/createAndUpdateRoleModelRequest.model';
import { RolePermissionResponse } from 'src/app/model/Role/rolePermissionResponse.model';
import { RoleReqeest } from 'src/app/model/Role/roleRequest.model';
import { RoleResponse } from 'src/app/model/Role/roleResponse.model';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { RoleService } from 'src/app/shared/Service/RoleService/role.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';

@Component({
  selector: 'app-create-and-update-role',
  templateUrl: './create-and-update-role.component.html',
  styleUrls: ['./create-and-update-role.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class CreateAndUpdateRoleComponent implements OnInit {

  @Input('createAndUpdateRoleModelRequest') createAndUpdateRoleModelRequest: CreateAndUpdateRoleModelRequest;

  permissionSetting: MultiSelectDropdownSettings = null;
  permissionList: RolePermissionResponse[] = [];

  roleObject: RoleResponse = null;
  isFormReload: boolean = false;

  //MaxLength Message
  textBoxMaxLengthMessage: string = Small_TextBoxMaxCharactersAllowedMessage;
  textAreaMaxLengthMessage: string = TextAreaMaxCharactersAllowedMessage;
  specialCharacterErrorMessage: string = SpecialCharacterErrorMessage;

  createRoleForm = new FormGroup({
    roleName: new FormControl('', [Validators.required, Validators.maxLength(Small_TextBoxMaxLength), this.commonsService.removeSpacesThrowError(), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    permission: new FormControl([], [Validators.required]),
    description: new FormControl('', [Validators.maxLength(TextAreaMaxLength)])
  });

  constructor(
    private activeModal: NgbActiveModal,
    private roleService: RoleService,
    private roleApiCallService: RoleApiCallService,
    private toste: ToastrService,
    private commonsService: CommonsService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService) { }

  public ngOnInit(): void {
    this.permissionSetting = this.multiSelectDropDownSettingService.getPermissionDrpSetting(true);
    this.getRolePermissionList();
  }

  /**
   * Get Permission List
   * <AUTHOR>
   */
  private async getRolePermissionList(): Promise<void> {
    this.permissionList = await this.roleApiCallService.getRolePermissionList(this.createAndUpdateRoleModelRequest.resourseName);
    this.setData(this.createAndUpdateRoleModelRequest.resourseName);
  }

  /**
   * This function set form data if user try to Update Role 
   * <AUTHOR>
   * 
   */
  private setData(resourseName: string): void {
    if (this.createAndUpdateRoleModelRequest.isUpdateData) {
      this.roleService.isLoading(true, resourseName);
      this.roleApiCallService.getRoleDetail(this.createAndUpdateRoleModelRequest.roleId).subscribe({
        next: (res: HttpResponse<RoleResponse>) => {
          if (res.status == 200) {
            this.roleObject = res.body;
            this.createRoleForm.get('roleName').setValue(this.roleObject.name);
            this.createRoleForm.get('description').setValue(this.roleObject.description);
            this.createRoleForm.get('permission').setValue(this.roleObject.permissions);
          }
          this.roleService.isLoading(false, resourseName);
          this.isFormReload = true;
        }, error: (error: HttpErrorResponse) => {
          this.createAndUpdateRoleException(resourseName, error);
        }
      });
    } else {
      this.isFormReload = true;
    }
  }

  /**
   * Close Popup
   * <AUTHOR>
   * 
   */
  public decline(): void {
    this.activeModal.close(false);
  }

  /**
   * 
   * This function get form data and convert RoleReqeest Object
   * <AUTHOR>
   * @returns RoleReqeest
   */
  private getRoleResponseObject(): RoleReqeest {
    let formValue = this.createRoleForm.value;
    let permissionId = this.roleService.getPermissionIdList(formValue.permission);
    return new RoleReqeest(this.commonsService.checkNullFieldValue(formValue.roleName), this.commonsService.checkNullFieldValue(formValue.description), permissionId);
  }

  /**
   * Close Popup
  * <AUTHOR>
  * 
  */
  private accept(): void {
    this.activeModal.close(true);
  }

  /**
   * This function call the api for create and update role
   * isUpdateData -> true means Role Update api call other vise create role api call
   * <AUTHOR>
   */
  public addOrUpdateRole() {
    if (this.createAndUpdateRoleModelRequest.isUpdateData) {
      this.updateRole(this.roleObject.id,
        this.getRoleResponseObject(), this.createAndUpdateRoleModelRequest.resourseName,
        this.createAndUpdateRoleModelRequest.isFilterHidden);
    } else {
      this.createRole(this.getRoleResponseObject(), this.createAndUpdateRoleModelRequest.resourseName,
        this.createAndUpdateRoleModelRequest.isFilterHidden);
    }

  }

  /**
   * Add Role Api Call
   * <AUTHOR>
   * @param roleResponse 
   * @param resourceName 
   */
  private createRole(roleResponse: RoleReqeest, resourceName: string, isFilterHidden): void {
    this.roleService.isLoading(true, resourceName);
    this.roleApiCallService.createRole(roleResponse).subscribe({
      next: (res: HttpResponse<SuccessMessageResponse>) => {
        this.createAndUpdateRoleAfter(res.body.message, resourceName, isFilterHidden);
      }, error: (error: HttpErrorResponse) => {
        this.createAndUpdateRoleException(resourceName, error);
      }
    });
  }

  /**
   * Update Role Api Call
   * <AUTHOR>
   * @param roleId 
   * @param roleResponse 
   * @param resourceName 
   */
  private updateRole(roleId: number, roleResponse: RoleReqeest, resourceName: string, isFilterHidden: boolean) {
    this.roleService.isLoading(true, resourceName);
    this.roleApiCallService.updateRole(roleId, roleResponse).subscribe({
      next: (res: HttpResponse<SuccessMessageResponse>) => {
        this.createAndUpdateRoleAfter(res.body.message, resourceName, isFilterHidden);
      }, error: (error: HttpErrorResponse) => {
        this.createAndUpdateRoleException(resourceName, error);
      }
    });
  }

  /**
   * Call Filter compoent subject with pass pagereload parameter
   * <AUTHOR>
   * @param message 
   * @param resourceName 
   * 
   */
  private createAndUpdateRoleAfter(message: string, resourceName: string, isFilterHidden: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
    this.roleApiCallService.roleModifySuccessAfterAction(listingPageReloadSubjectParameter, message, resourceName, isFilterHidden);
    this.accept();
  }


  /**
   * Close the loading and Display Error message
   * <AUTHOR>
   * @param resourceName 
   * @param error 
   */
  private createAndUpdateRoleException(resourceName: string, error: HttpErrorResponse): void {
    if (error.status == 409) {
      this.roleService.isLoading(false, resourceName);
      this.toste.error("Role already exist");
    } else {
      this.roleApiCallService.roleModifyErrorAfterAction(resourceName, error);
    }

  }

}
