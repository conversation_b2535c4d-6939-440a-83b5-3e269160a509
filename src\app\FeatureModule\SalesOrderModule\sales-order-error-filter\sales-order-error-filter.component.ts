import { Component, Input } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { isUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { SPECIAL_CHARACTER_PATTERN, SpecialCharacterErrorMessage, Small_TextBoxMaxCharactersAllowedMessage, Small_TextBoxMaxLength } from 'src/app/app.constants';
import { SalesOrderFaieldSearchRequestBody } from 'src/app/model/SalesOrder/SalesOrderFaieldSearchRequestBody.model';
import { SalesOrderFailedFilterAction } from 'src/app/model/SalesOrder/SalesOrderFailedFilterAction.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { SalesOrderService } from 'src/app/shared/Service/SalesOrderService/sales-order.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';

@Component({
  selector: 'app-sales-order-error-filter',
  templateUrl: './sales-order-error-filter.component.html',
  styleUrls: ['./sales-order-error-filter.component.css']
})
export class SalesOrderErrorFilterComponent {

  @Input("isFilterComponentInitWithApicall") isFilterComponentInitWithApicall: boolean;
  @Input("salesOrderFaieldSearchRequestBody") salesOrderFaieldSearchRequestBody: SalesOrderFaieldSearchRequestBody;

  //MaxLength Message
  small_textBoxMaxLengthMessage: string = Small_TextBoxMaxCharactersAllowedMessage;
  specialCharacterErrorMessage: string = SpecialCharacterErrorMessage;

  filterSalesOrderFailedForm = new FormGroup({
    salesOrderNumber: new FormControl('', [Validators.maxLength(Small_TextBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)])
  });

  subscriptionForRefeshList: Subscription;

  defaultListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

  constructor(
    private salesOrderService: SalesOrderService,
    private commonsService: CommonsService,
    private commonOperationsService: CommonOperationsService) { }

  public ngOnInit(): void {
    this.onInitSubject();
    this.getFilterList();
    if (this.isFilterComponentInitWithApicall) {
      this.clearFilter(this.defaultListingPageReloadSubjectParameter);
    }
  }

  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForRefeshList)) { this.subscriptionForRefeshList.unsubscribe() }
  }

  /**
   * Get Filter List
   * 
   * <AUTHOR>
   */
  public async getFilterList(): Promise<void> {
    this.setFilterValue();
  }

  /**
   * Set Filter value 
   * 
   * <AUTHOR>
   */
  private setFilterValue(): void {
    if (this.salesOrderFaieldSearchRequestBody != null) {
      this.filterSalesOrderFailedForm.get('salesOrderNumber').setValue(this.salesOrderFaieldSearchRequestBody.salesOrderNumber);
    }
  }


  public onInitSubject(): void {
    /**
     * Sales Order Detail Page Refresh After some Action Like Create or Update or Delete Sales Order
     * <AUTHOR>
     */
    this.subscriptionForRefeshList = this.salesOrderService.getSalesOrderFailedListRefreshSubject().subscribe((listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
      if (listingPageReloadSubjectParameter.isReloadData) {
        if (listingPageReloadSubjectParameter.isClearFilter) {
          this.clearFilter(listingPageReloadSubjectParameter);
        } else {
          //page change 1,2,3
          this.salesOrderFailedListPageRefresh(listingPageReloadSubjectParameter);
        }
      }
    });
  }


  /**
   * Reload Listing Data
   * <AUTHOR>
   */
  public searchData(): void {
    let allFormValue = this.filterSalesOrderFailedForm.value;
    this.filterSalesOrderFailedForm.get('salesOrderNumber').setValue(this.commonsService.checkNullFieldValue(allFormValue.salesOrderNumber));
    if (this.filterSalesOrderFailedForm.invalid ||
      (this.commonsService.checkValueIsNullOrEmpty(allFormValue.salesOrderNumber))) {
      this.commonOperationsService.showEmptyFilterTosteMessge();
    } else {
      this.salesOrderFailedListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
   * Clear All filter and Reload Data
   * <AUTHOR>
   */
  public clearFilter(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    this.filterSalesOrderFailedForm.get('salesOrderNumber').setValue("");
    this.salesOrderFailedListPageRefresh(listingPageReloadSubjectParameter);
  }

  /**
   * Get Filter Data and pass to Listing page and Reload Page 
   * <AUTHOR>
   */
  private salesOrderFailedListPageRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    let allFormValue = this.filterSalesOrderFailedForm.value;
    let saleOrderFaieldRequestBody = new SalesOrderFaieldSearchRequestBody(
      this.commonsService.checkNullFieldValue(allFormValue.salesOrderNumber)
    );
    let salesOrderFilterAction = new SalesOrderFailedFilterAction(listingPageReloadSubjectParameter, saleOrderFaieldRequestBody);
    this.salesOrderService.callSalesOrderFailedListFilterRequestParameterSubject(salesOrderFilterAction);
  }


}
