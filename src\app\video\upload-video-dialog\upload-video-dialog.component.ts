import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Medium_TextBoxMaxCharactersAllowedMessage, Medium_TextBoxMaxLength, SPECIAL_CHARACTER_PATTERN, SpecialCharacterErrorMessage, TextAreaMaxCharactersAllowedMessage, TextAreaMaxLength, UpdateVideoSuccessMessage, UploadVideoSuccessMessage } from 'src/app/app.constants';
import { Subtitles, UploadVideoRequest } from 'src/app/model/video/upload-video-request.model';
import { UploadVideoResponse } from 'src/app/model/video/upload-video-response.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { InventoryService } from 'src/app/shared/inventory.service';
import { SubjectMessageService } from 'src/app/shared/subject-message.service';
import { VideoService } from 'src/app/shared/videoservice/video.service';

@Component({
  selector: 'app-upload-video-dialog',
  templateUrl: './upload-video-dialog.component.html',
  styleUrls: ['./upload-video-dialog.component.css']
})
export class UploadVideoDialogComponent implements OnInit {

  @Input() title: string;
  @Input() message: string;
  @Input() videoZipFileId: number;
  @Input() isEditable: boolean;
  @Input() btnOkText: string;
  @Input() btnCancelText: string;

  disableBtn = true;
  loading = false;

  //getVideo response
  videoInformation: UploadVideoRequest;

  //file type validation
  validVideoFileType: string = null;
  validThumbnailFileType: string = null;
  validSrtFileType: string = null;

  //select file input
  videoFile: any;
  videoChanged: boolean;
  thumbnailFile: any;
  thumbnailChanged: boolean;
  srtFiles: any[] = [];
  subtitles: Subtitles[] = [];
  isSrt: boolean;

  videoFileName: string;
  videoFileSize: number;
  thumbnailFileName: string;
  thumbnailFileSize: number;

  //clear file input
  @ViewChild('videoFileInput') videoFileInput: ElementRef;
  @ViewChild('thumbnailFileInput') thumbnailFileInput: ElementRef;
  @ViewChild('srtFileInput') srtFileInput: ElementRef;

  visible = true;
  selectable = true;
  removable = true;

  //max character message
  textBoxMaxCharactersAllowedMessage: string = Medium_TextBoxMaxCharactersAllowedMessage;
  textAreaMaxLengthMessage: string = TextAreaMaxCharactersAllowedMessage;
  specialCharacterErrorMessage: string = SpecialCharacterErrorMessage;

  uploadVideoForm = new FormGroup({
    Title: new FormControl('', [Validators.required, Validators.maxLength(Medium_TextBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    duration: new FormControl('', [Validators.required, Validators.pattern('^[0-9:]+')]),
    videoFileSelect: new FormControl('', [Validators.required]),
    thumbnailFileSelect: new FormControl('', [Validators.required]),
    srtFileSelect: new FormControl(''),
    notes: new FormControl('', [Validators.maxLength(TextAreaMaxLength)]),
    srtFilesList: new FormArray([])
  });

  disabled = false;

  constructor(
    private activeModal: NgbActiveModal,
    private inventoryService: InventoryService,
    private videoService: VideoService,
    private exceptionService: ExceptionHandlingService,
    public subjectMessageService: SubjectMessageService,
    private toastrService: ToastrService
  ) { }

  ngOnInit(): void {
    if (!isNullOrUndefined(this.videoZipFileId)) {
      this.getVideo(this.videoZipFileId);
    }
  }

  public getVideo(videoId: number): void {
    this.subjectMessageService.setLoading(true);
    this.videoService.getVideo(videoId).subscribe({
      next: (videoResponse: HttpResponse<UploadVideoRequest>) => {
        this.subjectMessageService.setLoading(false);
        this.videoInformation = videoResponse.body;
        this.setValueToForm(this.videoInformation);
      }, error: (error: HttpErrorResponse) => {
        this.subjectMessageService.setLoading(false);
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  public setValueToForm(videoInformation) {
    this.disableBtn = false;
    this.uploadVideoForm.get("Title").setValue(videoInformation.title);
    this.uploadVideoForm.get("duration").setValue(videoInformation.duration);

    this.videoFile = videoInformation.videoFile;
    this.videoFileName = videoInformation.videoFile;
    this.videoFileSize = videoInformation.videoFileSize;
    this.videoChanged = videoInformation.videoChanged;

    this.thumbnailFile = videoInformation.thumbnailFile;
    this.thumbnailFileName = videoInformation.thumbnailFile;
    this.thumbnailFileSize = videoInformation.thumbnailFileSize;
    this.thumbnailChanged = videoInformation.thumbnailChanged;

    this.listSubtitles(videoInformation.subtitles);
    this.uploadVideoForm.get("notes").setValue(videoInformation.notes);
    this.disableFormControls();
  }

  public listSubtitles(subtitles: Subtitles[]) {
    for (let subtitleObj of subtitles) {
      const add = this.uploadVideoForm.get("srtFilesList") as FormArray;
      let srtFileGroup = {};
      srtFileGroup["title"] = new FormControl('', Validators.required);
      srtFileGroup["title"].setValue(subtitleObj.title);
      srtFileGroup["subtitleFile"] = new FormControl();
      srtFileGroup["subtitleFile"].setValue(subtitleObj.subtitleFile);
      srtFileGroup["changed"] = new FormControl();
      srtFileGroup["changed"].setValue(subtitleObj.changed);
      srtFileGroup["subTitleFileSize"] = new FormControl();
      srtFileGroup["subTitleFileSize"].setValue(subtitleObj.subTitleFileSize);
      let srtFileForm = new FormGroup(srtFileGroup);
      add.push(srtFileForm);
      let srtObj = new Subtitles(subtitleObj.title, subtitleObj.subtitleFile, subtitleObj.changed, subtitleObj.subTitleFileSize);
      this.subtitles.push(srtObj);
      this.srtFiles.push(srtObj);
    }
  }

  /**
   * disable controls of upload video form
   */
  private disableFormControls(): void {
    if (!this.isEditable) {
      this.uploadVideoForm.get("Title").disable();
      this.uploadVideoForm.get("duration").disable();
      this.uploadVideoForm.get("srtFileSelect").disable();
      this.uploadVideoForm.get("srtFilesList").disable();
    }
  }

  handleVideoFileInput(event) {
    if (!isNullOrUndefined(event.target.files)) {
      this.videoFile = event.target.files[0];
      if (this.videoFile.type != undefined) {
        if (this.videoFile.type == 'video/mp4') {
          this.videoFileName = this.videoFile.name;
          this.videoFileSize = this.videoFile.size;
          this.videoChanged = true;
          this.validVideoFileType = null;
          this.disableBtn = isNullOrUndefined(this.videoZipFileId);
        } else {
          this.validVideoFileType = "select mp4 file only";
          (<HTMLInputElement>document.getElementById("videofile")).value = null;
          this.disableBtn = true;
        }
      }
    }
  }

  handleThumbnailFileInput(event) {
    if (!isNullOrUndefined(event.target.files)) {
      this.thumbnailFile = event.target.files[0];
      if (this.thumbnailFile.type != undefined) {
        if (this.thumbnailFile.type == 'image/jpeg' || this.thumbnailFile.type == 'image/jpg') {
          this.thumbnailFileName = this.thumbnailFile.name;
          this.thumbnailFileSize = this.thumbnailFile.size;
          this.thumbnailChanged = true;
          this.validThumbnailFileType = null;
          this.disableBtn = isNullOrUndefined(this.videoZipFileId);
        } else {
          this.validThumbnailFileType = "select jpg/jpeg file only";
          (<HTMLInputElement>document.getElementById("thumbnailfile")).value = null;
          this.disableBtn = true;
        }
      }
    }
  }

  handleSrtFileInput(event) {
    if (!isNullOrUndefined(event.target.files)) {
      let srts = event.target.files;
      for (let srtFileObj of srts) {
        let srtname = srtFileObj.name;
        if ((srtname.substring(srtname.indexOf(".") + 1)) != 'srt') {
          this.isSrt = false;
          this.validSrtFileType = "select srt file only";
          (<HTMLInputElement>document.getElementById("srtfile")).value = null;
          return;
        } else {
          this.validSrtFileType = null;
          this.isSrt = true;
        }
      }
      if (this.isSrt) {
        for (let srtFileObj of srts) {
          let srtFileTitle = srtFileObj.name.substring(srtFileObj.name.indexOf('.'), 0);
          const add = this.uploadVideoForm.get("srtFilesList") as FormArray;
          let srtFileGroup = {};
          srtFileGroup["title"] = new FormControl('', Validators.required);
          srtFileGroup["title"].setValue(srtFileTitle);
          srtFileGroup["subtitleFile"] = new FormControl();
          srtFileGroup["subtitleFile"].setValue(srtFileObj.name);
          srtFileGroup["changed"] = new FormControl();
          srtFileGroup["changed"].setValue(true);
          srtFileGroup["subTitleFileSize"] = new FormControl();
          srtFileGroup["subTitleFileSize"].setValue(srtFileObj.size);
          let srtFileForm = new FormGroup(srtFileGroup);
          add.push(srtFileForm);
          let srtObj = new Subtitles(srtFileTitle, srtFileObj.name, true, srtFileObj.size);
          this.subtitles.push(srtObj);
          this.srtFiles.push(srtFileObj);
        }
      }
    }
  }

  removeSrt(form: any, removeObject: any, field: string, index: number) {
    if (field == "srtFilesList") {
      form.get(field).removeAt(index);
      this.subtitles.splice(index, 1);
      this.srtFiles.splice(index, 1);
      if (this.srtFiles.length == 0) {
        this.reset(this.srtFileInput);
      }
    } else if (field == "videoFile") {
      this.videoFile = null;
      this.videoFileName = null;
      this.videoFileSize = null;
      this.disableBtn = true;
      form.get('videoFileSelect').setValue(null);
      this.reset(this.videoFileInput);
    } else if (field == "thumbnailFile") {
      this.thumbnailFile = null;
      this.thumbnailFileName = null;
      this.thumbnailFileSize = null;
      this.disableBtn = true;
      form.get('thumbnailFileSelect').setValue(null);
      this.reset(this.thumbnailFileInput);
    }
  }

  reset(fileInput) {
    if (!isNullOrUndefined(fileInput)) {
      fileInput.nativeElement.value = "";
    }
  }

  public getUploadVideoDialogInformation(uploadVideoForm): UploadVideoRequest {
    return new UploadVideoRequest(this.uploadVideoForm.get("Title").value, this.uploadVideoForm.get("duration").value, this.videoFileName, this.videoFileSize, this.videoChanged, this.thumbnailFileName, this.thumbnailFileSize, this.thumbnailChanged, this.uploadVideoForm.get("srtFilesList").value, uploadVideoForm.notes);
  }

  public accept(): void {
    this.subjectMessageService.setLoading(true);
    let uploadVideoDialogRequest: UploadVideoRequest = this.getUploadVideoDialogInformation(this.uploadVideoForm.value);
    if (!isNullOrUndefined(this.videoZipFileId)) {
      this.videoService.updateVideo(uploadVideoDialogRequest, this.videoZipFileId, {
        uploaded: false
      }).subscribe({
        next: (validationFilesResponse: HttpResponse<UploadVideoResponse>) => {
          if (validationFilesResponse.status == 200) {
            this.uploadFilesToBlob(uploadVideoDialogRequest, validationFilesResponse, true);
          }
        }, error: (error: HttpErrorResponse) => {
          this.subjectMessageService.setLoading(false);
          this.exceptionService.customErrorMessage(error);
        }
      });
    } else {
      this.videoService.uploadVideo(uploadVideoDialogRequest, {
        uploaded: false
      })?.subscribe({
        next: (validationFilesResponse: HttpResponse<UploadVideoResponse>) => {
          if (validationFilesResponse.status == 200) {
            this.uploadFilesToBlob(uploadVideoDialogRequest, validationFilesResponse, false);
          }
        }, error: (error: HttpErrorResponse) => {
          this.subjectMessageService.setLoading(false);
          this.exceptionService.customErrorMessage(error);
        }
      });
    }
  }

  public uploadFilesToBlob(uploadVideoDialogRequest: UploadVideoRequest, validationFilesResponse, isUpdate: boolean): void {
    if (uploadVideoDialogRequest.thumbnailChanged) {
      this.inventoryService.uploadFileToStorage(this.thumbnailFile, validationFilesResponse.body.thumbnailUrl).subscribe(res => { });
    }
    this.srtFiles.forEach((srt) => {
      let srtUrl = validationFilesResponse.body.subTitles.filter(subtitle => { if (subtitle.name.indexOf(srt.name) !== -1 && subtitle.subTitleChanged) return subtitle });
      if (srtUrl.length == 1) {
        this.inventoryService.uploadFileToStorage(srt, srtUrl[0].url).subscribe(res => { });
      }
    });
    if (uploadVideoDialogRequest.videoChanged) {
      this.inventoryService.uploadFileToStorage(this.videoFile, validationFilesResponse.body.videoUrl).subscribe(res => {
        this.uploadFileToStorageSuccess(isUpdate, uploadVideoDialogRequest);
      });
    } else {
      this.uploadFileToStorageSuccess(isUpdate, uploadVideoDialogRequest);
    }
  }

  /**
   * Upload File To Stoare Success
   * 
   * <AUTHOR>
   * @param isUpdate 
   * @param uploadVideoDialogRequest 
   */
  private uploadFileToStorageSuccess(isUpdate: boolean, uploadVideoDialogRequest: UploadVideoRequest): void {
    if (isUpdate) {
      this.updateVideoFinalStatus(uploadVideoDialogRequest);
    } else {
      this.uploadVideoFinalStatus(uploadVideoDialogRequest);
    }
  }

  /**
   * Upload Video Final Status Update
   * 
   * <AUTHOR>
   * @param uploadVideoDialogRequest 
   */
  private uploadVideoFinalStatus(uploadVideoDialogRequest: UploadVideoRequest): void {
    this.videoService.uploadVideo(uploadVideoDialogRequest, { uploaded: true }).subscribe({
      next: () => {
        this.updateAndUploadSuccessResponse(UploadVideoSuccessMessage);
      }, error: (error: HttpErrorResponse) => {
        this.updateAndUploadErrorResponse(error);
      }
    });
  }

  /**
   * Update Video Final Status Update
   * 
   * <AUTHOR>
   * @param uploadVideoDialogRequest 
   */
  private updateVideoFinalStatus(uploadVideoDialogRequest: UploadVideoRequest): void {
    this.videoService.updateVideo(uploadVideoDialogRequest, this.videoZipFileId, { uploaded: true }).subscribe({
      next: () => {
        this.updateAndUploadSuccessResponse(UpdateVideoSuccessMessage);
      }, error: (error: HttpErrorResponse) => {
        this.updateAndUploadErrorResponse(error);
      }
    });
  }

  /**
   * Upload/Update Video Success Response
   * 
   * <AUTHOR>
   */
  private updateAndUploadSuccessResponse(message: string): void {
    this.subjectMessageService.setLoading(false);
    this.toastrService.success(message);
    this.activeModal.close(true);
  }

  /**
   * Upload/Update Video Error Response
   * 
   * <AUTHOR>
   * @param error 
   */
  private updateAndUploadErrorResponse(error: HttpErrorResponse): void {
    this.subjectMessageService.setLoading(false);
    this.exceptionService.customErrorMessage(error);
  }

  public decline(): void {
    this.activeModal.close(false);
  }

}
