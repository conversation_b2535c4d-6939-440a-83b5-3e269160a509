import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { Cancel, DEVICE_ALREADY_CLIENT, DEVICE_ALREADY_DEMO, DEVICE_ALREADY_EDIT_DISABLE, DEVICE_ALREADY_EDIT_ENABLE, DEVICE_ALREADY_LOCKED, DEVICE_ALREADY_TEST, DEVICE_ALREADY_UNLOCKED, DEVICE_DELETE, DEVICE_HAS_NO_SERIAL_NUMBER, DEVICE_STATUS_NOT_ENABLE, DeviceDetailResource, ITEMS_PER_PAGE, SALES_ORDER_PARTIALLY_CONFIGURED, SalesOrderAssociationHeader, Submit, TRANSFER_ORDER } from '../app.constants';
import { ConfirmDialogService } from '../confirmationdialog/confirmation.service';
import { CountryListResponse } from '../model/Country/CountryListResponse.model';
import { BooleanKeyValueMapping } from '../model/common/BooleanKeyValueMapping.model';
import { EnumMapping } from '../model/common/EnumMapping.model';
import { SuccessMessageResponse } from '../model/common/SuccessMessageResponse.model';
import { CustomerAssociationModelRequest } from '../model/customer-association-model/customer-association-model-request.model';
import { CustomerAssociationRequest } from '../model/customer-association-request';
import { AssignSelectedReleaseVersionRequest } from '../model/device/AssignSelectedReleaseVersionRequest.model';
import { TransferProductDetails } from '../model/device/TransferProductDetails.model';
import { DeviceDetailResponse } from '../model/device/deviceDetailResponse.model';
import { ReleaseVersionRequest } from '../model/release-version-request.model';
import { ExceptionHandlingService } from '../shared/ExceptionHandling.service';
import { CountryCacheService } from '../shared/Service/CacheService/countrycache.service';
import { DeviceActionService } from '../shared/device-action.service';
import { DeviceService } from '../shared/device.service';
import { ProductStatusEnum } from '../shared/enum/Common/ProductStatus.enum';
import { DeviceListOperations } from '../shared/enum/Operations/DeviceListOperations.enum';
import { PermissionAction } from '../shared/enum/Permission/permissionAction.enum';
import { ProductConfigStatus } from '../shared/enum/SalesOrder/ProductConfigStatus.enum';
import { deviceTypesEnum } from '../shared/enum/deviceTypesEnum.enum';
import { CustomerAssociationService } from '../shared/modalservice/customer-association.service';
import { PermissionService } from '../shared/permission.service';
import { EnumMappingDisplayNamePipe } from '../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { CommonOperationsService } from '../shared/util/common-operations.service';
import { KeyValueMappingServiceService } from '../shared/util/key-value-mapping-service.service';
import { ModuleValidationServiceService } from '../shared/util/module-validation-service.service';
import { ValidationService } from '../shared/util/validation.service';

@Component({
  selector: 'app-device-detail',
  templateUrl: './device-detail.component.html',
  styleUrls: ['./device-detail.component.css']
})
export class DeviceDetailComponent implements OnInit {
  @Input("deviceIdInput") deviceIdInput: any;
  @Input("resource") resource: string;
  @Output("showDevice") showDevice = new EventEmitter;
  countryList: Array<CountryListResponse> = [];

  deviceDetailResponse: DeviceDetailResponse = null;

  loading: boolean = false;
  releaseVersions: ReleaseVersions[] = [];
  releaseVersionId: number;
  selectedReleaseVersion: number = -1;
  btnReleaseVersionDisable: boolean = true;

  //Page Dispaly
  deviceDetailDisplay: boolean = false;
  transferOrderSelectionDisaplay: boolean = false;

  subscriptionForCommonloading: Subscription;

  itemsPerPage = ITEMS_PER_PAGE;
  page = 0;

  deviceOperations: string[] = [];

  //Permission
  updateDeviceTypePermission: boolean = false;

  //Option List
  productStatusList: Array<EnumMapping> = [];
  lockUnlockStatus: Array<BooleanKeyValueMapping> = [];
  editEnableDisableStatus: Array<BooleanKeyValueMapping> = [];
  deviceDetailsTrasferProduct: TransferProductDetails = null

  constructor(protected deviceService: DeviceService,
    private toster: ToastrService,
    private exceptionService: ExceptionHandlingService,
    private customerAssociationService: CustomerAssociationService,
    private permissionService: PermissionService,
    private commonOperationsService: CommonOperationsService,
    private keyValueMappingServiceService: KeyValueMappingServiceService,
    private toste: ToastrService,
    private confirmDialogService: ConfirmDialogService,
    private enumMappingDisplayNamePipe: EnumMappingDisplayNamePipe,
    private validationService: ValidationService,
    private countryCacheService: CountryCacheService,
    private moduleValidationServiceService: ModuleValidationServiceService,
    private deviceActionService: DeviceActionService) { }

  public async ngOnInit() {
    this.loading = true;
    this.countryList = await this.countryCacheService.getCountryListFromCache(false);
    this.updateDeviceTypePermission = this.permissionService.getDevicePermission(PermissionAction.UPDATE_DEVICE_TYPE_ACTION);
    //Get Product Status Option list
    this.productStatusList = this.keyValueMappingServiceService.enumOptionToList(ProductStatusEnum);
    //Get Locked/Unlocked Option list
    this.lockUnlockStatus = this.keyValueMappingServiceService.lockedUnlockOptionList();
    this.editEnableDisableStatus = this.keyValueMappingServiceService.editEnableDisableOptionList();
    this.deviceDetailDisplay = true;
    this.deviceDetailModel(this.deviceIdInput);
    this.subjectInit();
  }

  ngOnDestroy() {
    if (this.subscriptionForCommonloading != undefined) {
      this.subscriptionForCommonloading.unsubscribe();
    }
  }

  private subjectInit(): void {
    this.subscriptionForCommonloading = this.commonOperationsService.getCommonLoadingSubject()?.subscribe((res: boolean) => {
      this.loading = res;
    });
  }

  deviceDetailModel(deviceId: number) {
    this.loading = true;
    this.deviceService.getDeviceDetail(deviceId)?.subscribe(
      {
        next: (res: HttpResponse<DeviceDetailResponse>) => {
          if (res.status == 200) {
            this.deviceDetailResponse = res.body;
            this.releaseVersionId = (res?.body?.releaseId == null) ? -1 : res.body.releaseId;
            this.getReleaseVersions(this.deviceDetailResponse.deviceType, this.deviceDetailResponse.countryId, this.deviceDetailResponse.packageVersion);
            this.deviceDetailsTrasferProduct = new TransferProductDetails(this.deviceIdInput, this.deviceDetailResponse.salesOrderId, this.deviceDetailResponse.deviceSerialNo, null, DeviceDetailResource);
            let transferProduct: boolean = this.deviceDetailResponse.orderRecordType === TRANSFER_ORDER;
            this.deviceOperations = this.commonOperationsService.accessDeviceListOperations(false, transferProduct, this.resource);
          } else {
            this.toster.warning(DEVICE_DELETE);
            this.loading = false;
            this.back();
          }
        },
        error: (error: HttpErrorResponse) => {
          this.loading = false;
          this.exceptionService.customErrorMessage(error);
        }
      });
  }

  private getReleaseVersions(deviceType: deviceTypesEnum, countryId: number, packageVersion: string): void {
    if (deviceType == deviceTypesEnum.TEST_DEVICE && this.updateDeviceTypePermission) {
      if (!isNullOrUndefined(countryId)) {
        this.loading = true;
        let requestObject = new ReleaseVersionRequest(countryId, packageVersion);
        this.deviceService.getReleaseVersionDetail(requestObject)?.subscribe({
          next: (response: any) => {
            this.onAvailReleaseVersions(response);
            this.loading = false;
          }, error: (error) => { this.loading = false; }
        });
      } else {
        this.setEmptyReleaseVersions();
        this.loading = false;
      }
    } else {
      this.loading = false;
    }
  }

  private setEmptyReleaseVersions(): void {
    this.releaseVersions = [];
    this.selectedReleaseVersion = -1;
    this.toggleAssignButton();
  }

  private onAvailReleaseVersions(response: any): void {
    if (response.status == 200) {
      this.releaseVersions = response.body;
      this.selectedReleaseVersion = JSON.parse(JSON.stringify(this.releaseVersionId));
      this.toggleAssignButton();
    } else {
      this.setEmptyReleaseVersions();
    }
  }

  public changeReleaseVersion(event): void {
    let changedValue = event.target.value;
    let item = this.releaseVersions.filter(releaseVersion => releaseVersion.id == changedValue);
    if (item.length == 1) {
      this.selectedReleaseVersion = item[0].id;
    } else {
      this.selectedReleaseVersion = -1;
    }
    this.toggleAssignButton();
  }

  public assignReleaseVersion(): void {
    if (this.validateWithUserInfoAndDeviceInfo()) {
      this.loading = true;
      let assignSelectedReleaseVersionRequest = new AssignSelectedReleaseVersionRequest(this.deviceDetailResponse.id, this.selectedReleaseVersion);
      this.deviceService.assignSelectedReleaseVersion(assignSelectedReleaseVersionRequest)?.subscribe({
        next: (response: HttpResponse<SuccessMessageResponse>) => {
          this.deviceDetailModel(this.deviceIdInput);
          this.tosterAsPerResponseStatus(response.status);
        }, error: (error: HttpErrorResponse) => {
          this.tosterAsPerResponseStatus(error.status);
          this.loading = false;
        }
      });
    }
  }

  private toggleAssignButton(): void {
    if (-1 === this.selectedReleaseVersion || this.selectedReleaseVersion === this.releaseVersionId) {
      this.btnReleaseVersionDisable = true;
    } else {
      this.btnReleaseVersionDisable = false;
    }
  }

  private tosterAsPerResponseStatus(status: number): void {
    (status == 200) ? this.toster.success("Release version assigned successfully") : this.toster.error("Error in assigning Release version");
  }

  back() {
    this.showDevice.emit();
  }

  /**
   * Device Opertions list 
   */
  public changeDeviceOperation(event): void {
    let selectedOperation = event.target.value;
    switch (selectedOperation) {
      case DeviceListOperations.UNLOCK_DEVICES:
        this.lockUnlock(false);
        break;
      case DeviceListOperations.LOCK_DEVICES:
        this.lockUnlock(true);
        break;
      case DeviceListOperations.CUSTOMER_SALES_ORDER_ASSOCIATION: {
        this.associationDeviceWithSalesOrder();
        break;
      }
      case DeviceListOperations.DISABLED_DEVICES:
        this.validateProductStatusForDisableAction();
        break;
      case DeviceListOperations.EDIT_ENABLE_DEVICE:
        this.enableDisableDevice(true);
        break;
      case DeviceListOperations.EDIT_DISABLE_DEVICE:
        this.enableDisableDevice(false);
        break;
      case DeviceListOperations.RMA_DEVICES:
        this.validateProductStatusForRMAAction();
        break;
      case DeviceListOperations.SET_DEVICE_TO_TEST:
        this.convertDataToTest();
        break;
      case DeviceListOperations.SET_DEVICE_TO_CLIENT:
        this.convertDataToClient();
        break;
      case DeviceListOperations.SET_DEVICE_TO_DEMO:
        this.convertDataToDemo();
        break;
      case DeviceListOperations.TRANSFER_DEVICE:
        this.transferDevice();
        break;
      default:
        break;
    }
    let selection = document.getElementById('deviceOperation') as HTMLSelectElement;
    selection.value = DeviceListOperations.DeviceOperations;
  }

  /**
   * Associaton or Update customer email
   * 
   * <AUTHOR>
   */
  private associationDeviceWithSalesOrder(): void {
    if (this.validateWithUserInfoAndDeviceInfo()) {
      this.customerAssociationService.openCustomerAssociationPopup(new CustomerAssociationModelRequest(DeviceDetailResource, SalesOrderAssociationHeader, Submit, Cancel, this.deviceDetailResponse.salesOrderNumber))
        .then((confirmedRequestBody: CustomerAssociationRequest) => {
          if (confirmedRequestBody.button) {
            this.loading = true;
            this.deviceService.associationDeviceWithSalesOrder([this.deviceDetailResponse.id], confirmedRequestBody.basicSalesOrderDetailResponse)
              ?.subscribe(
                {
                  next: (res: HttpResponse<SuccessMessageResponse>) => {
                    this.ngOnInit();
                    this.loading = false;
                    this.toster.success(res.body.message);
                  },
                  error: (error: HttpErrorResponse) => {
                    this.loading = false;
                    this.exceptionService.customErrorMessage(error);
                  }
                }
              );
          }
        });
    }
  }

  /**
   * Validate Product Status For Disable Action
   * 
   * <AUTHOR>
   */
  private validateProductStatusForDisableAction(): void {
    if (this.validateWithUserInfoAndDeviceInfo()) {
      this.disableProductStatusForDevice();
    }
  }

  /**
   * Editable Enable And Disable
   * 
   * <AUTHOR>
   */
  private async enableDisableDevice(editable: boolean): Promise<void> {
    if (this.deviceDetailResponse.editable === editable) {
      editable ? this.toste.info(DEVICE_ALREADY_EDIT_ENABLE) : this.toste.info(DEVICE_ALREADY_EDIT_DISABLE);
    } else {
      if (this.validateUserCountry()) {
        this.loading = true;
        try {
          await this.deviceActionService.deviceEditAction([this.deviceDetailResponse.id], editable)
          this.deviceDetailModel(this.deviceIdInput);
        } catch (error) {
          this.loading = false;
        }
      }
    }
  }

  /**
  *  Locked And unlocked
  * 
  * <AUTHOR>
  */
  public lockUnlock(lockState: boolean): void {
    if (this.validateWithUserInfoAndDeviceInfo()) {
      if (this.deviceDetailResponse.locked === lockState) {
        lockState ? this.toste.info(DEVICE_ALREADY_LOCKED) : this.toste.info(DEVICE_ALREADY_UNLOCKED);
      } else {
        if (this.validateUserCountry()) {
          this.loading = true;
          try {
            this.lockUnlockDeviceApicall(lockState);
          } catch (error) {
            this.loading = false;
          }
        }
      }
    }
  }

  /**
  *  Locked And unlocked API calls
  * 
  * <AUTHOR>
  */
  private lockUnlockDeviceApicall(lockState: boolean): void {
    this.loading = true;
    this.deviceService.updateDeviceState([this.deviceDetailResponse.id], lockState).subscribe({
      next: (res: HttpResponse<SuccessMessageResponse>) => {
        if (res.status == 200) {
          this.toste.success(res.body.message);
          this.deviceDetailModel(this.deviceIdInput);
        } else {
          this.loading = false;
        }
      }, error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
 * Convert Device Type as Test
 * 
 * <AUTHOR>
 */
  public convertDataToTest(): void {
    if (this.validateWithUserInfoAndDeviceInfo()) {
      if (this.deviceDetailResponse.deviceType != deviceTypesEnum.TEST_DEVICE) {
        this.loading = true;
        this.deviceService.updateDeviceTypeToTest([this.deviceDetailResponse.id]).subscribe({
          next: (res: HttpResponse<SuccessMessageResponse>) => {
            if (res.status == 200) {
              this.toste.success(res.body.message);
              this.deviceDetailModel(this.deviceIdInput);
            }
          }, error: (error: HttpErrorResponse) => {
            this.loading = false;
            this.exceptionService.customErrorMessage(error);
          }
        });
      } else {
        this.toste.info(DEVICE_ALREADY_TEST);
      }
    }
  }

  /**
  * transfer Device
  * 
  * <AUTHOR>
  */
  public transferDevice(): void {
    if (this.validateWithUserInfoAndDeviceInfo()) {
      if (this.deviceDetailResponse.deviceSerialNo === null) {
        this.toster.info(DEVICE_HAS_NO_SERIAL_NUMBER);
      }
      else if (ProductStatusEnum[this.deviceDetailResponse.productStatus] !== ProductStatusEnum.ENABLED) {
        this.toster.info(DEVICE_STATUS_NOT_ENABLE);
      }
      else if (ProductConfigStatus[this.deviceDetailResponse.soStatus] === ProductConfigStatus.PARTIALLY_CONFIGURED) {
        this.toster.info(SALES_ORDER_PARTIALLY_CONFIGURED);
      } else {
        this.transferOrderSelectionToggle(false, true);
      }
    }
  }

  /**
  * Convert Device Type as Demo
  * 
  * <AUTHOR>
  */
  public convertDataToDemo(): void {
    if (this.validateWithUserInfoAndDeviceInfo()) {
      if (this.deviceDetailResponse.deviceType != deviceTypesEnum.DEMO_DEVICE) {
        this.loading = true;
        this.deviceService.updateDeviceTypeToDemo([this.deviceDetailResponse.id])?.subscribe({
          next: (res: HttpResponse<SuccessMessageResponse>) => {
            if (res.status == 200) {
              this.toste.success(res.body.message);
              this.deviceDetailModel(this.deviceIdInput);
            }
          }, error: (error: HttpErrorResponse) => {
            this.loading = false;
            this.exceptionService.customErrorMessage(error);
          }
        });
      } else {
        this.toste.info(DEVICE_ALREADY_DEMO);
      }
    }
  }

  /**
  * Convert Device Type as Client
  * 
  * <AUTHOR>
  */
  public convertDataToClient(): void {
    if (this.validateWithUserInfoAndDeviceInfo()) {
      if (this.deviceDetailResponse.deviceType != deviceTypesEnum.CLIENT_DEVICE) {
        this.loading = true;
        this.deviceService.updateDeviceTypeToClient([this.deviceDetailResponse.id]).subscribe({
          next: (res: HttpResponse<SuccessMessageResponse>) => {
            if (res.status == 200) {
              this.toste.success(res.body.message);
              this.deviceDetailModel(this.deviceIdInput);
            }
          }, error: (error: HttpErrorResponse) => {
            this.loading = false;
            this.exceptionService.customErrorMessage(error);
          }
        });
      } else {
        this.toste.info(DEVICE_ALREADY_CLIENT);
      }
    }
  }

  /**
   * Validate Product Status For Disable Action
   * 
   * <AUTHOR>
   */
  private validateProductStatusForRMAAction(): void {
    if (this.validateWithUserInfoAndDeviceInfo()) {
      let currentProductStatus = this.enumMappingDisplayNamePipe.transform(this.deviceDetailResponse.productStatus, this.productStatusList);
      if (this.validationService.validateProductStatusForRMAAction(currentProductStatus)) {
        this.rmaProductStatusForDevice();
      } else {
        this.confirmDialogService.getErrorMessageDisableToRma(DeviceDetailResource);
      }
    }
  }

  /**
   * Disable Product Status For Device
   * 
   * <AUTHOR>
   * @param productStatus 
   */
  private disableProductStatusForDevice(): void {
    let basicModelConfig = this.confirmDialogService.getBasicModelConfigForDisableAction(DeviceDetailResource);
    this.confirmDialogService.confirm(basicModelConfig.title, basicModelConfig.message, basicModelConfig.btnOkText, basicModelConfig.btnCancelText).then((res: boolean) => {
      if (res) {
        this.loading = true;
        this.deviceService.disableProductStatusForDevice([this.deviceDetailResponse.id])?.subscribe(
          {
            next: (response: HttpResponse<SuccessMessageResponse>) => {
              this.deviceDetailModel(this.deviceIdInput);
              this.toste.success(response.body.message)
            },
            error: (error: HttpErrorResponse) => {
              this.loading = false;
              this.exceptionService.customErrorMessage(error);
            }
          });
      }
    }).finally(() => { })
  }

  /**
   * RMA Product Status For Device
   * 
   * <AUTHOR>
   */
  private rmaProductStatusForDevice(): void {
    let basicModelConfig = this.confirmDialogService.getBasicModelConfigForRMAAction(DeviceDetailResource);
    this.confirmDialogService.confirm(basicModelConfig.title, basicModelConfig.message, basicModelConfig.btnOkText, basicModelConfig.btnCancelText).then((res: boolean) => {
      if (res) {
        this.loading = true;
        this.deviceService.rmaProductStatusForDevice([this.deviceDetailResponse.id])?.subscribe(
          {
            next: (response: HttpResponse<SuccessMessageResponse>) => {
              this.deviceDetailModel(this.deviceIdInput);
              this.toste.success(response.body.message)
            },
            error: (error: HttpErrorResponse) => {
              this.loading = false;
              this.exceptionService.customErrorMessage(error);
            }
          });
      }
    }).finally(() => { })
  }

  /**
  * Toggles the display states for device details and transfer order sections.
  * 
  * <AUTHOR>
  * 
  * @param deviceDetailDisplay 
  * @param tranferOrderSectionDisplay
  */
  public transferOrderSelectionToggle(deviceDetailDisplay: boolean, tranferOrderSectionDisplay: boolean) {
    this.transferOrderSelectionDisaplay = tranferOrderSectionDisplay;
    this.deviceDetailDisplay = deviceDetailDisplay;
    if (deviceDetailDisplay) {
      this.deviceDetailModel(this.deviceIdInput);
    }
  }

  /**
  * Refresh Device Detail Page Data
  * 
  * <AUTHOR> 
  */
  public refreshDeviceDetailPage(): void {
    this.deviceDetailModel(this.deviceIdInput);
  }

  /**
   * Validation With Device Editable or not
   * 
   * <AUTHOR>
   * @returns 
   */
  private validateWithUserInfoAndDeviceInfo(): boolean {
    return this.moduleValidationServiceService.validateWithEditStateForSingleRecord(this.deviceDetailResponse?.editable, DeviceDetailResource) ? this.moduleValidationServiceService.validateWithUserCountryForSingleRecord(this.deviceDetailResponse?.country, DeviceDetailResource, true) : false;
  }

  /**
   * Probe Validation According to assign country
   * 
   * <AUTHOR>
   * @returns 
   */
  private validateUserCountry(): boolean {
    return this.moduleValidationServiceService.validateWithUserCountryForSingleRecord(this.deviceDetailResponse.country, DeviceDetailResource, true);
  }

}

export class ReleaseVersions {
  public id: number;
  public itemNumber: string;
}
