import { Component, Input, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { isNullOrUndefined, isUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { SPECIAL_CHARACTER_PATTERN, SpecialCharacterErrorMessage, Small_TextBoxMaxCharactersAllowedMessage, Small_TextBoxMaxLength, TextBoxMaxCharactersAllowedMessage } from 'src/app/app.constants';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { PresetDetailBaseResponse } from 'src/app/model/Presets/PresetDetailBaseResponse.model';
import { ProbeConfigGroupFilterAction } from 'src/app/model/ProbeConfigGroup/ProbeConfigGroupFilterAction.model';
import { ProbeConfigGroupRequestBody } from 'src/app/model/ProbeConfigGroup/ProbeConfigGroupRequestBody.model';
import { EnumMapping } from 'src/app/model/common/EnumMapping.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { FeaturesFilter } from 'src/app/model/probe/FeaturesFilter.model';
import { ProbeFeatureResponse } from 'src/app/model/probe/ProbeFeatureResponse.model';
import { PresetApiService } from 'src/app/shared/Service/PresetService/preset-api.service';
import { ProbeConfigGroupService } from 'src/app/shared/Service/ProbeConfigGroup/probe-config-group.service';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { ProbeService } from 'src/app/shared/Service/ProbeService/probe.service';
import { ProbeTypeEnum } from 'src/app/shared/enum/ProbeType.enum';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';

@Component({
  selector: 'app-probe-config-group-filter',
  templateUrl: './probe-config-group-filter.component.html',
  styleUrl: './probe-config-group-filter.component.css',
  encapsulation: ViewEncapsulation.None,
})
export class ProbeConfigGroupFilterComponent {
  @Input('isFilterComponentInitWithApicall') isFilterComponentInitWithApicall: boolean;
  @Input("listPageRefreshForbackToDetailPage") listPageRefreshForbackToDetailPage: boolean;
  @Input('probeConfigGroupRequestBody') probeConfigGroupRequestBody: ProbeConfigGroupRequestBody;

  dropdownSettingsForProbeType: MultiSelectDropdownSettings = null;
  dropdownSettingsFeature: MultiSelectDropdownSettings = null;
  dropdownSettingsPreset: MultiSelectDropdownSettings = null;

  probeTypesList: Array<EnumMapping> = [];
  featureList: Array<FeaturesFilter> = [];
  presetList: Array<PresetDetailBaseResponse> = [];

  //MaxLength Message
  small_textBoxMaxLengthMessage: string = Small_TextBoxMaxCharactersAllowedMessage;
  textBoxMaxLengthMessage: string = TextBoxMaxCharactersAllowedMessage;
  specialCharacterErrorMessage: string = SpecialCharacterErrorMessage;


  probeConfigGroupForm = new FormGroup({
    partNumber: new FormControl('', [
      Validators.maxLength(Small_TextBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)
    ]),
    probeType: new FormControl([], []),
    featureType: new FormControl([], []),
    presetType: new FormControl([], []),
  });

  subscriptionForRefeshList: Subscription;

  defaultListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

  constructor(
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private commonsService: CommonsService,
    private probeApiService: ProbeApiService,
    private probeService: ProbeService,
    private commonOperationsService: CommonOperationsService,
    private probeConfigGroupService: ProbeConfigGroupService,
    private keyValueMappingService: KeyValueMappingServiceService,
    private presetApiService: PresetApiService,
  ) { }

  public ngOnInit(): void {
    this.dropdownSettingsForProbeType = this.multiSelectDropDownSettingService.getProbeConfigGroupTypeDropdownSetting();
    this.dropdownSettingsFeature = this.multiSelectDropDownSettingService.getFeatureDrpSetting();
    this.dropdownSettingsPreset = this.multiSelectDropDownSettingService.getPresetDrpSetting();
    this.getInitData();
    this.onInitSubject();
    if (this.isFilterComponentInitWithApicall) {
      this.clearFilter(this.defaultListingPageReloadSubjectParameter);
    }
  }

  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForRefeshList)) {
      this.subscriptionForRefeshList.unsubscribe();
    }
  }

  public async getInitData(): Promise<void> {
    if (this.probeConfigGroupService.getFeaturesList().length === 0) {
      let featuresResponse: Array<ProbeFeatureResponse> = await this.probeApiService.getFeaturesList();
      this.featureList = this.probeService.getFeaturesListForFilter(featuresResponse, false);
      this.probeConfigGroupService.setFeaturesList(this.featureList);
    } else {
      this.featureList = this.probeConfigGroupService.getFeaturesList();
    }
    if (this.probeConfigGroupService.getPresetsList().length === 0) {
      this.presetList = await this.presetApiService.getProbePresetsList();
      this.probeConfigGroupService.setPresetsList(this.presetList);
    }
    else {
      this.presetList = this.probeConfigGroupService.getPresetsList();
    }
    this.probeTypesList = this.keyValueMappingService.enumOptionToList(ProbeTypeEnum);
    this.setFilterValue();
  }

  public onInitSubject(): void {
    /**
     * Probe feature group Detail Page Refresh After some Action Like Create or Update or Delete Sales Order
     * <AUTHOR>
     */
    this.subscriptionForRefeshList = this.probeConfigGroupService
      .getProbeConfigGroupListRefreshSubject()
      ?.subscribe(
        (listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
          if (listingPageReloadSubjectParameter.isReloadData) {
            if (listingPageReloadSubjectParameter.isClearFilter) {
              this.clearFilter(listingPageReloadSubjectParameter);
            } else {
              //page change 1,2,3
              this.probeConfigGroupListPageRefresh(listingPageReloadSubjectParameter)
            }
          }
        }
      );
  }

  /**
   * Set Filter value
   *
   * Note : if user hide and show filter then set data in storage
   *
   * <AUTHOR>
   */
  private setFilterValue(): void {
    if (this.probeConfigGroupRequestBody != null) {
      let probeType = this.commonsService.getEnumMappingSelectedValue(ProbeTypeEnum, this.probeConfigGroupRequestBody.probeTypes);
      let features = this.featureList.filter(feature => !isNullOrUndefined(this.probeConfigGroupRequestBody.features) && this.probeConfigGroupRequestBody.features.includes(feature.id));
      let preserts = this.presetList.filter(preset => !isNullOrUndefined(this.probeConfigGroupRequestBody.presets) && this.probeConfigGroupRequestBody.presets.includes(preset.id));
      this.probeConfigGroupForm.get('partNumber').setValue(this.probeConfigGroupRequestBody.partNumberCode);
      this.probeConfigGroupForm.get('probeType').setValue(probeType);
      this.probeConfigGroupForm.get('featureType').setValue(features);
      this.probeConfigGroupForm.get('presetType').setValue(preserts);
    }
    if (this.listPageRefreshForbackToDetailPage) {
      this.probeConfigGroupListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
   * Reload Listing Data
   * <AUTHOR>
   */
  public searchData(): void {
    let allFormValue = this.probeConfigGroupForm.value;
    this.probeConfigGroupForm.get('partNumber').setValue(this.commonsService.checkNullFieldValue(allFormValue.partNumber));
    if (
      this.probeConfigGroupForm.invalid ||
      (this.commonsService.checkValueIsNullOrEmpty(allFormValue.partNumber) &&
        this.commonsService.checkValueIsNullOrEmpty(allFormValue.probeType) &&
        this.commonsService.checkValueIsNullOrEmpty(allFormValue.featureType) &&
        this.commonsService.checkValueIsNullOrEmpty(allFormValue.presetType))
    ) {
      this.commonOperationsService.showEmptyFilterTosteMessge();
    } else {
      this.probeConfigGroupListPageRefresh(
        this.defaultListingPageReloadSubjectParameter
      );
    }
  }

  /**
   * Clear All filter and Reload Data
   * <AUTHOR>
   */
  public clearFilter(
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter
  ): void {
    this.probeConfigGroupForm.get('partNumber').setValue('');
    this.probeConfigGroupForm.get('probeType').setValue([]);
    this.probeConfigGroupForm.get('featureType').setValue([]);
    this.probeConfigGroupForm.get('presetType').setValue([]);
    this.probeConfigGroupListPageRefresh(listingPageReloadSubjectParameter);
  }

  /**
   * Get Filter Data and pass to Listing page and Reload Page
   * <AUTHOR>
   */
  private probeConfigGroupListPageRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    if (this.probeConfigGroupForm.invalid) {
      this.probeConfigGroupForm.reset();
    }
    let allFormValue = this.probeConfigGroupForm.value;
    let featureType = this.commonsService.getIdsFromArray(allFormValue.featureType);
    let presetType = this.commonsService.getIdsFromArray(allFormValue.presetType);
    let probeType = this.commonsService.getSelectedValueFromEnum(allFormValue.probeType)
    let probeConfigGroupRequestBody = new ProbeConfigGroupRequestBody(this.commonsService.checkNullFieldValue(allFormValue.partNumber), probeType, featureType, presetType);
    let probeConfigGroupFilterAction = new ProbeConfigGroupFilterAction(listingPageReloadSubjectParameter, probeConfigGroupRequestBody);
    this.probeConfigGroupService.callProbeConfigGroupListFilterRequestParameterSubject(probeConfigGroupFilterAction);
  }
}
