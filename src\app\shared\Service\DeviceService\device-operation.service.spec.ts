import { TestBed } from '@angular/core/testing';
import { Subject, of, throwError } from 'rxjs';
import { DeviceOperationService } from './device-operation.service';
import { DeviceSearchRequest } from 'src/app/model/device/deviceSearchRequest.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { DeviceService } from '../../device.service';
import { SalesOrderApiCallService } from '../SalesOrderService/sales-order-api-call.service';
import { CountryCacheService } from '../CacheService/countrycache.service';
import { CommonsService } from '../../util/commons.service';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ToastrService } from 'ngx-toastr';
import { AuthJwtService } from '../../auth-jwt.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { DeviceListResource } from 'src/app/app.constants';
import { DeviceFilterAction } from 'src/app/model/device/DeviceFilterAction.model';

describe('DeviceOperationService', () => {
  let service: DeviceOperationService;
  let deviceService: jasmine.SpyObj<DeviceService>;
  let salesOrderApiCallService: jasmine.SpyObj<SalesOrderApiCallService>;
  let countryCacheService: jasmine.SpyObj<CountryCacheService>;
  let commonsService: jasmine.SpyObj<CommonsService>;
  let exceptionHandlingService: jasmine.SpyObj<ExceptionHandlingService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;

  beforeEach(() => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const deviceServiceSpy = jasmine.createSpyObj('DeviceService', ['getpackageVersion']);
    const salesOrderApiCallServiceSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderNumberList']);
    const countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache']);
    const commonsServiceSpy = jasmine.createSpyObj('CommonsService', ['checkForNull']);
    const exceptionHandlingServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);

    TestBed.configureTestingModule({
      providers: [
        { provide: DeviceService, useValue: deviceServiceSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionHandlingServiceSpy },
        AuthJwtService,
        LocalStorageService,
        SessionStorageService,
        commonsProviders(toastrServiceMock)
      ]
    });

    service = TestBed.inject(DeviceOperationService);
    deviceService = TestBed.inject(DeviceService) as jasmine.SpyObj<DeviceService>;
    salesOrderApiCallService = TestBed.inject(SalesOrderApiCallService) as jasmine.SpyObj<SalesOrderApiCallService>;
    countryCacheService = TestBed.inject(CountryCacheService) as jasmine.SpyObj<CountryCacheService>;
    commonsService = TestBed.inject(CommonsService) as jasmine.SpyObj<CommonsService>;
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService) as jasmine.SpyObj<ExceptionHandlingService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get device list filter request parameter subject', () => {
    expect(service.getDeviceListFilterRequestParameterSubject()).toBeInstanceOf(Subject);
  });

  it('should get device list refresh subject', () => {
    expect(service.getDeviceListRefreshSubject()).toBeInstanceOf(Subject);
  });

  it('should set and get package version list', () => {
    const packageVersions = ['v1.0.0', 'v1.1.0', 'v2.0.0'];
    service.setPackageVersionList(packageVersions);
    expect(service.getPackageVersionList()).toEqual(packageVersions);
  });

  it('should set and get sales order number list', () => {
    const salesOrderNumbers = ['SO001', 'SO002', 'SO003'];
    service.setSalesOrderNumberList(salesOrderNumbers);
    expect(service.getSalesOrderNumberList()).toEqual(salesOrderNumbers);
  });

  it('should set and get country list', () => {
    const countries: CountryListResponse[] = [
      { id: 1, country: 'USA', languages: ['English'] },
      { id: 2, country: 'Canada', languages: ['English', 'French'] }
    ];
    service.setCountryList(countries);
    expect(service.getCountryList()).toEqual(countries);
  });

  it('should initialize with empty cache arrays', () => {
    expect(service.getPackageVersionList()).toEqual([]);
    expect(service.getSalesOrderNumberList()).toEqual([]);
    expect(service.getCountryList()).toEqual([]);
  });

  it('should maintain separate cache for each data type', () => {
    const packageVersions = ['v1.0.0'];
    const salesOrderNumbers = ['SO001'];
    const countries: CountryListResponse[] = [{ id: 1, country: 'USA', languages: ['English'] }];

    service.setPackageVersionList(packageVersions);
    service.setSalesOrderNumberList(salesOrderNumbers);
    service.setCountryList(countries);

    expect(service.getPackageVersionList()).toEqual(packageVersions);
    expect(service.getSalesOrderNumberList()).toEqual(salesOrderNumbers);
    expect(service.getCountryList()).toEqual(countries);
  });

  it('should allow updating cached data', () => {
    // Set initial data
    service.setPackageVersionList(['v1.0.0']);
    expect(service.getPackageVersionList()).toEqual(['v1.0.0']);

    // Update with new data
    service.setPackageVersionList(['v1.0.0', 'v2.0.0']);
    expect(service.getPackageVersionList()).toEqual(['v1.0.0', 'v2.0.0']);
  });

  it('should update cache in background with fresh API data', async () => {
    // Setup API responses
    const apiPackageVersions = ['v3.0.0', 'v4.0.0'];
    const apiSalesOrders = ['SO004', 'SO005'];
    const apiCountries: CountryListResponse[] = [
      { id: 3, country: 'Germany', languages: ['German'] }
    ];

    deviceService.getpackageVersion.and.returnValue(of({ body: apiPackageVersions } as any));
    salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(apiSalesOrders));
    countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve(apiCountries));
    commonsService.checkForNull.and.returnValue(apiPackageVersions);

    // Call updateCacheInBackground
    await service.updateCacheInBackground();

    // Verify cache is updated with fresh data
    expect(service.getPackageVersionList()).toEqual(apiPackageVersions);
    expect(service.getSalesOrderNumberList()).toEqual(apiSalesOrders);
    expect(service.getCountryList()).toEqual(apiCountries);

    // Verify API calls were made
    expect(deviceService.getpackageVersion).toHaveBeenCalled();
    expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
    expect(countryCacheService.getCountryListFromCache).toHaveBeenCalledWith(true);
  });

  it('should update only sales order cache when updateSalesOrderCacheOnly is called', async () => {
    // Setup initial cache with some data
    service.setPackageVersionList(['v1.0.0']);
    service.setSalesOrderNumberList(['SO001']);
    service.setCountryList([{ id: 1, country: 'USA', languages: ['English'] }]);

    // Setup API response for sales orders only
    const newSalesOrders = ['SO001', 'SO002', 'SO003'];
    salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(newSalesOrders));

    // Call updateSalesOrderCacheOnly
    await service.updateSalesOrderCacheOnly();

    // Verify only sales order cache is updated
    expect(service.getSalesOrderNumberList()).toEqual(newSalesOrders);

    // Verify other caches remain unchanged
    expect(service.getPackageVersionList()).toEqual(['v1.0.0']);
    expect(service.getCountryList()).toEqual([{ id: 1, country: 'USA', languages: ['English'] }]);

    // Verify only sales order API was called
    expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
    expect(deviceService.getpackageVersion).not.toHaveBeenCalled();
    expect(countryCacheService.getCountryListFromCache).not.toHaveBeenCalled();
  });

  describe('callDeviceListFilterRequestParameterSubject', () => {
    it('should emit device filter action through subject', () => {
      const deviceSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      const deviceFilterAction = new DeviceFilterAction(listingPageReloadSubjectParameter, deviceSearchRequest);

      spyOn(service.getDeviceListFilterRequestParameterSubject(), 'next');

      service.callDeviceListFilterRequestParameterSubject(deviceFilterAction);

      expect(service.getDeviceListFilterRequestParameterSubject().next).toHaveBeenCalledWith(deviceFilterAction);
    });
  });

  describe('callDeviceListRefreshSubject', () => {
    it('should emit refresh parameters through subject', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

      spyOn(service.getDeviceListRefreshSubject(), 'next');

      service.callDeviceListRefreshSubject(listingPageReloadSubjectParameter);

      expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalledWith(listingPageReloadSubjectParameter);
    });
  });

  describe('callRefreshPageSubject', () => {
    it('should call device list filter when resource is DeviceListResource and filter is hidden', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      listingPageReloadSubjectParameter.isClearFilter = false;
      const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        DeviceListResource,
        true,
        deviceSearchRequest
      );

      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
        jasmine.any(DeviceFilterAction)
      );
    });

    it('should call device list filter with empty search request when isClearFilter is true', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
      const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        DeviceListResource,
        true,
        deviceSearchRequest
      );

      const expectedDeviceFilterAction = jasmine.objectContaining({
        deviceSearchRequest: jasmine.objectContaining({
          packageVersions: null
        })
      });

      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(expectedDeviceFilterAction);
    });

    it('should call device list filter with empty search request when deviceSearchRequestBodyApply is null', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        DeviceListResource,
        true,
        null
      );

      const expectedDeviceFilterAction = jasmine.objectContaining({
        deviceSearchRequest: jasmine.objectContaining({
          packageVersions: null
        })
      });

      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(expectedDeviceFilterAction);
    });

    it('should call device list refresh subject when filter is not hidden', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

      spyOn(service.getDeviceListRefreshSubject(), 'next');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        DeviceListResource,
        false,
        deviceSearchRequest
      );

      expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalledWith(listingPageReloadSubjectParameter);
    });

    it('should not call any subject when resource is not DeviceListResource', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');
      spyOn(service.getDeviceListRefreshSubject(), 'next');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        'OTHER_RESOURCE',
        true,
        deviceSearchRequest
      );

      expect(service.callDeviceListFilterRequestParameterSubject).not.toHaveBeenCalled();
      expect(service.getDeviceListRefreshSubject().next).not.toHaveBeenCalled();
    });
  });

  describe('Edge cases and boundary conditions', () => {
    it('should handle empty arrays in cache setters', () => {
      service.setPackageVersionList([]);
      service.setSalesOrderNumberList([]);
      service.setCountryList([]);

      expect(service.getPackageVersionList()).toEqual([]);
      expect(service.getSalesOrderNumberList()).toEqual([]);
      expect(service.getCountryList()).toEqual([]);
    });

    it('should handle large arrays in cache', () => {
      const largePackageVersions = Array.from({ length: 1000 }, (_, i) => `v${i}.0.0`);
      const largeSalesOrders = Array.from({ length: 1000 }, (_, i) => `SO${i.toString().padStart(6, '0')}`);
      const largeCountries = Array.from({ length: 200 }, (_, i) => ({ id: i, country: `Country${i}`, languages: ['Language'] }));

      service.setPackageVersionList(largePackageVersions);
      service.setSalesOrderNumberList(largeSalesOrders);
      service.setCountryList(largeCountries);

      expect(service.getPackageVersionList()).toEqual(largePackageVersions);
      expect(service.getSalesOrderNumberList()).toEqual(largeSalesOrders);
      expect(service.getCountryList()).toEqual(largeCountries);
    });

    it('should handle undefined deviceSearchRequestBodyApply in callRefreshPageSubject', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        DeviceListResource,
        true,
        undefined as any
      );

      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
        jasmine.objectContaining({
          deviceSearchRequest: jasmine.objectContaining({
            packageVersions: null
          })
        })
      );
    });
  });

  describe('Private method coverage through public methods', () => {
    describe('getPackageVersionsFromAPI coverage', () => {
      it('should handle successful package version API call', async () => {
        const mockResponse = { body: ['v1.0.0', 'v2.0.0'] };
        deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
        salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
        countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));
        commonsService.checkForNull.and.returnValue(['v1.0.0', 'v2.0.0']);

        await service.updateCacheInBackground();

        expect(deviceService.getpackageVersion).toHaveBeenCalled();
        expect(commonsService.checkForNull).toHaveBeenCalledWith(['v1.0.0', 'v2.0.0']);
        expect(service.getPackageVersionList()).toEqual(['v1.0.0', 'v2.0.0']);
      });

      it('should handle null response body from package version API', async () => {
        const mockResponse = { body: null };
        deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
        salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
        countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));
        commonsService.checkForNull.and.returnValue(null);

        await service.updateCacheInBackground();

        expect(commonsService.checkForNull).toHaveBeenCalledWith(null);
        expect(service.getPackageVersionList()).toEqual([]);
      });

      it('should handle undefined response body from package version API', async () => {
        const mockResponse = { body: undefined };
        deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
        salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
        countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));
        commonsService.checkForNull.and.returnValue(undefined);

        await service.updateCacheInBackground();

        expect(commonsService.checkForNull).toHaveBeenCalledWith(undefined);
        expect(service.getPackageVersionList()).toEqual([]);
      });
    });

    describe('getSalesOrderNumbersFromAPI coverage', () => {
      it('should handle successful sales order API call', async () => {
        const mockSalesOrders = ['SO001', 'SO002', 'SO003'];
        deviceService.getpackageVersion.and.returnValue(of({ body: ['v1.0.0'] } as any));
        salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(mockSalesOrders));
        countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));
        commonsService.checkForNull.and.returnValue(['v1.0.0']);

        await service.updateCacheInBackground();

        expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
        expect(service.getSalesOrderNumberList()).toEqual(mockSalesOrders);
      });
    });

    describe('getCountriesFromAPI coverage', () => {
      it('should handle successful countries API call', async () => {
        const mockCountries = [
          { id: 1, country: 'USA', languages: ['English'] },
          { id: 2, country: 'Canada', languages: ['English', 'French'] }
        ];
        deviceService.getpackageVersion.and.returnValue(of({ body: ['v1.0.0'] } as any));
        salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
        countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve(mockCountries));
        commonsService.checkForNull.and.returnValue(['v1.0.0']);

        await service.updateCacheInBackground();

        expect(countryCacheService.getCountryListFromCache).toHaveBeenCalledWith(true);
        expect(service.getCountryList()).toEqual(mockCountries);
      });
    });
  });

  describe('updateSalesOrderCacheOnly method coverage', () => {
    it('should call getSalesOrderNumbersFromAPI and update cache', async () => {
      const mockSalesOrders = ['SO100', 'SO101', 'SO102'];
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(mockSalesOrders));

      await service.updateSalesOrderCacheOnly();

      expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
      expect(service.getSalesOrderNumberList()).toEqual(mockSalesOrders);
    });
  });

  describe('Complete line coverage for callRefreshPageSubject', () => {
    it('should cover the line where deviceSearchRequest is assigned from deviceSearchRequestBodyApply', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      const deviceSearchRequest = new DeviceSearchRequest(['test'], 'active', 'type1', 'device123', 'serial123', 'customer1', [1, 2], true, false, ['SO001'], null);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        DeviceListResource,
        true,
        deviceSearchRequest
      );

      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
        jasmine.objectContaining({
          deviceSearchRequest: deviceSearchRequest
        })
      );
    });

    it('should cover the line where new DeviceFilterAction is created', () => {
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      service.callRefreshPageSubject(
        listingPageReloadSubjectParameter,
        DeviceListResource,
        true,
        deviceSearchRequest
      );

      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
        jasmine.any(DeviceFilterAction)
      );
    });
  });

  describe('Constructor and initialization coverage', () => {
    it('should initialize with injected dependencies', () => {
      expect(service).toBeTruthy();
      expect(service.getPackageVersionList()).toEqual([]);
      expect(service.getSalesOrderNumberList()).toEqual([]);
      expect(service.getCountryList()).toEqual([]);
      expect(service.getDeviceListFilterRequestParameterSubject()).toBeInstanceOf(Subject);
      expect(service.getDeviceListRefreshSubject()).toBeInstanceOf(Subject);
    });
  });

  describe('Additional coverage for uncovered statements', () => {

    it('should handle commonsService.checkForNull returning null in getPackageVersionsFromAPI', async () => {
      const mockResponse = { body: ['v1.0.0'] };
      deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      // Make commonsService.checkForNull return null to test the || [] fallback
      commonsService.checkForNull.and.returnValue(null);

      await service.updateCacheInBackground();

      expect(service.getPackageVersionList()).toEqual([]);
    });

    it('should handle commonsService.checkForNull returning undefined in getPackageVersionsFromAPI', async () => {
      const mockResponse = { body: ['v1.0.0'] };
      deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      // Make commonsService.checkForNull return undefined to test the || [] fallback
      commonsService.checkForNull.and.returnValue(undefined);

      await service.updateCacheInBackground();

      expect(service.getPackageVersionList()).toEqual([]);
    });

    it('should handle response with undefined body in getPackageVersionsFromAPI', async () => {
      const mockResponse = { body: undefined };
      deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      commonsService.checkForNull.and.returnValue(undefined);

      await service.updateCacheInBackground();

      expect(commonsService.checkForNull).toHaveBeenCalledWith(undefined);
      expect(service.getPackageVersionList()).toEqual([]);
    });

    it('should handle response with no body property in getPackageVersionsFromAPI', async () => {
      const mockResponse = {};
      deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      commonsService.checkForNull.and.returnValue(undefined);

      await service.updateCacheInBackground();

      expect(commonsService.checkForNull).toHaveBeenCalledWith(undefined);
      expect(service.getPackageVersionList()).toEqual([]);
    });

    it('should handle the exact line where commonsService.checkForNull returns falsy value', async () => {
      const mockResponse = { body: ['v1.0.0'] };
      deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      // Make commonsService.checkForNull return a falsy value (empty array)
      commonsService.checkForNull.and.returnValue([]);

      await service.updateCacheInBackground();

      expect(commonsService.checkForNull).toHaveBeenCalledWith(['v1.0.0']);
      // The || [] should kick in when checkForNull returns empty array
      expect(service.getPackageVersionList()).toEqual([]);
    });

    it('should handle the exact line where commonsService.checkForNull returns null as any[]', async () => {
      const mockResponse = { body: ['v1.0.0'] };
      deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      // Make commonsService.checkForNull return null (cast as any[] to satisfy TypeScript)
      commonsService.checkForNull.and.returnValue(null as any);

      await service.updateCacheInBackground();

      expect(commonsService.checkForNull).toHaveBeenCalledWith(['v1.0.0']);
      // The || [] should kick in when checkForNull returns null
      expect(service.getPackageVersionList()).toEqual([]);
    });

    it('should test the specific conditional branch in getPackageVersionsFromAPI', async () => {
      // Test the specific line: return this.commonsService.checkForNull(response?.body) || [];
      const mockResponse = { body: null };
      deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));

      // Make commonsService.checkForNull return null to test the || [] fallback
      commonsService.checkForNull.and.returnValue(null as any);

      // Call the method directly through updateCacheInBackground to test the private method
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      await service.updateCacheInBackground();

      expect(commonsService.checkForNull).toHaveBeenCalledWith(null);
      expect(service.getPackageVersionList()).toEqual([]);
    });
  });
});
