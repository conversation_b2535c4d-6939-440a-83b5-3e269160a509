import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { FormArray, FormControl, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { lastValueFrom, Subscription } from 'rxjs';
import { DateDisplayFormat, DateTimeDisplayFormat, DELETE_SALES_ORDER_CONFIRMATION_MESSAGE, DetailSalesOrderResource, EnterQRCode, EnterValidSerialNumber, Find_Letter_Pattern, ITEMS_PER_PAGE, PROBE_ALREADY_EXIEST, ProbeSuccessMessage, SALES_ORDER_DELETED_OR_USER_DEFINED, SERIAL_NUMBER_PATTERN, SerialNumberExists, Small_TextBoxMaxCharactersAllowedMessage, Small_TextBoxMaxLength, TextBoxMaxLength, TRANSFER_ORDER, VIOLATION_UNIQUE_KEY_CONSTRAINT } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { SalesOrderBridgeDetailResponse } from 'src/app/model/SalesOrder/SalesOrderBridgeDetailResponse.model';
import { SalesOrderDetailResponse } from 'src/app/model/SalesOrder/SalesOrderDetailResponse.model';
import { SalesOrderHierarchyResponse } from 'src/app/model/SalesOrder/SalesOrderHierarchyResponse.model';
import { SalesOrderLicenceDetailResponse } from 'src/app/model/SalesOrder/SalesOrderLicenceDetailResponse.model';
import { SalesOrderPdfLetterResponse } from 'src/app/model/SalesOrder/SalesOrderPdfLetterResponse';
import { SalesOrderProbeDetailResponse } from 'src/app/model/SalesOrder/SalesOrderProbeDetailResponse.model';
import { SalesOrderProduct } from 'src/app/model/SalesOrder/SalesOrderProduct.model';
import { SalesOrderProductMappingRequest } from 'src/app/model/SalesOrder/SalesOrderProductMappingRequest.model';
import { SalesOrderProductMappingResponse } from 'src/app/model/SalesOrder/SalesOrderProductMappingResponse.model';
import { SerialNumberMapping } from 'src/app/model/SalesOrder/SerialNumberMapping.model';
import { BooleanKeyValueMapping } from 'src/app/model/common/BooleanKeyValueMapping.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { TransferProductDetails } from 'src/app/model/device/TransferProductDetails.model';
import { AddUpdateMultiProbeResponse } from 'src/app/model/probe/multiProbe/AddUpdateMultiProbeResponse.model';
import { MultipleProbeResponse } from 'src/app/model/probe/multiProbe/MultipleProbeResponse.model';
import { ProbeFeatureRequest } from 'src/app/model/probe/multiProbe/ProbeFeatureRequest.model';
import { ProbeRequest } from 'src/app/model/probe/multiProbe/ProbeRequest.model';
import { ProbeTypeResponse } from 'src/app/model/probe/probeTypeResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { ProbeService } from 'src/app/shared/Service/ProbeService/probe.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { SalesOrderOperationsEnum } from 'src/app/shared/enum/Operations/SalesOrderOperations.enum';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { ProbeTypeEnum } from 'src/app/shared/enum/ProbeType.enum';
import { ProductConfigStatus } from 'src/app/shared/enum/SalesOrder/ProductConfigStatus.enum';
import { SalesOrderStatus } from 'src/app/shared/enum/SalesOrder/SalesOrderStatus.enum';
import { SalesOrderTypeStatus } from 'src/app/shared/enum/SalesOrder/SalesOrderTypeStatus.enum';
import { PermissionService } from 'src/app/shared/permission.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { ValidationService } from 'src/app/shared/util/validation.service';

@Component({
  selector: 'app-sales-order-detail',
  templateUrl: './sales-order-detail.component.html',
  styleUrls: ['./sales-order-detail.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class SalesOrderDetailComponent implements OnInit {

  @Input("salesOrderId") salesOrderId;
  @Output("showSalesOrderList") showSalesOrderList = new EventEmitter();

  loading: boolean = false;
  dateDisplayFormat: string = DateDisplayFormat;
  dateTimeDisplayFormat: string = DateTimeDisplayFormat;

  // related product list
  relatedProductsDrpSelectSize: number = ITEMS_PER_PAGE;

  productConfigStatus = ProductConfigStatus;
  tranferOrder = TRANSFER_ORDER;
  detailSalesOrderResource = DetailSalesOrderResource;

  //Sales Order Related Product List
  relatedProductList: Array<SalesOrderProbeDetailResponse> = [];
  //related bridges
  bridgesResponse: Array<SalesOrderBridgeDetailResponse> = [];
  //related licenses
  licensesResponse: Array<SalesOrderLicenceDetailResponse> = [];
  parentSalesOrders: Array<SalesOrderHierarchyResponse> = [];
  childSalesOrders: Array<SalesOrderHierarchyResponse> = [];

  //Sales Order Details
  salesOrderDetail: SalesOrderDetailResponse = null;

  //Sales Order Array
  salesOrderIdList: number[] = [];

  maxLengthValidator: ValidatorFn = Validators.maxLength(TextBoxMaxLength);
  requiredValidator: ValidatorFn = Validators.required;

  //Product ConfigStatus enum
  productNotConfig = ProductConfigStatus.NOT_CONFIGURED;
  productConfigStatusEnum = ProductConfigStatus;
  //Sales Order enum
  salesOrderStatusEnum = SalesOrderStatus;
  salesOrderTypeStatus = SalesOrderTypeStatus;

  //Api Count For Loading
  totelApiCount = 1;
  apiResponseCount = 0;

  //subject
  subscriptionForCommonloading: Subscription;

  //serial Number Validation
  enterQRCode = EnterQRCode;
  small_maxCharactersAllowedMessage = Small_TextBoxMaxCharactersAllowedMessage;
  enterValidSerialNumber = EnterValidSerialNumber;
  serialNumberExists = SerialNumberExists;

  //form 
  formGroup = new FormGroup({
    serialNumberList: new FormArray([])
  });
  formIsValid: boolean = false;

  //local Storage Mapping
  localSerialNumberMapping: Array<SerialNumberMapping> = [];
  //Probe Type Response set
  probeTypeResponse: Array<ProbeTypeResponse> = [];

  //Hide Show Page
  salesOrderDetailPageDiplay: boolean = true;
  probeDetailPageDiplay: boolean = false;
  deviceDetailPageDiplay: boolean = false;
  transferOrderSelectionDisaplay: boolean = false;
  //EntityId for Probe or Device
  productEntityId: number = null;
  productEntityValue: number = null;
  salesOrderPartNumberMappingId: number = null;
  //create Probe Permission
  salesOrderProbeCreatePermission: boolean = false;

  //kit Display
  isKitDisplay: boolean = false;

  //MAP
  probeTypeWithPrefixMap = new Map<string, string>();

  //Reset Button permission
  resetButtonPermission: boolean = false;

  //Permission
  salesOrderAdminPermission: boolean = false;
  transferOrderPermission: boolean = false;
  devicePermission: boolean = false;
  probePermission: boolean = false;
  downloadSalesOrderLetterPermission: boolean = false;
  salesOrderDetailOperations: string[] = [];

  lockUnlockStatus: Array<BooleanKeyValueMapping> = [];
  transferSalesOrder: boolean = false;
  salesOrderValidForTransfer: boolean = false;

  transferProductDetail: TransferProductDetails;

  constructor(
    private commonsService: CommonsService,
    private salesOrderApiCallService: SalesOrderApiCallService,
    private exceptionService: ExceptionHandlingService,
    private probeApiService: ProbeApiService,
    private probeService: ProbeService,
    private toster: ToastrService,
    private permissionService: PermissionService,
    private validationService: ValidationService,
    private dialogservice: ConfirmDialogService,
    private keyValueMappingServiceService: KeyValueMappingServiceService,
    private commonOperationsService: CommonOperationsService,
    private downloadService: DownloadService,
  ) {

  }

  /**
   * Component Create 
   * 
   * <AUTHOR>
   */
  public ngOnInit(): void {
    this.salesOrderIdList.push(this.salesOrderId);
    this.getInitData();
    //Get Locked/Unlocked Option list
    this.lockUnlockStatus = this.keyValueMappingServiceService.lockedUnlockOptionList();
  }

  /**
   * Get Init data
   * 
   * <AUTHOR>
   */
  private async getInitData(): Promise<void> {
    if (this.salesOrderIdList.length > 0) {
      this.salesOrderId = this.salesOrderIdList[this.salesOrderIdList.length - 1];
    }
    this.setLoadingStatus(true);
    this.localSerialNumberMapping = [];
    this.salesOrderProbeCreatePermission = this.permissionService.getSalesOrderPermission(PermissionAction.SALES_ORDER_CREATE_PROBE_ACTION);
    this.resetButtonPermission = this.permissionService.getSalesOrderPermission(PermissionAction.SALES_ORDER_RESET_BRIDGE_ACTION);
    this.salesOrderAdminPermission = this.permissionService.getSalesOrderPermission(PermissionAction.SALES_ORDER_DELETE_ACTION);
    this.transferOrderPermission = this.permissionService.getSalesOrderPermission(PermissionAction.SALES_ORDER_TRANSFER_ORDER_ACTION);
    this.devicePermission = this.permissionService.getDevicePermission(PermissionAction.GET_DEVICE_ACTION);
    this.probePermission = this.permissionService.getProbPermission(PermissionAction.GET_PROB_ACTION);
    this.downloadSalesOrderLetterPermission = this.permissionService.getSalesOrderPermission(PermissionAction.DOWNLOAD_SALESORDER_LETTER_ACTION);

    if (this.salesOrderProbeCreatePermission) {
      this.probeTypeResponse = await this.probeApiService.getprobeTypeResponseList(true);
      this.probeTypeWithPrefixMap = this.probeService.prepareProbeTypeWithPrefixMap(this.probeTypeResponse);
    }
    this.getSalesOrderDetail(true);
  }

  public salesOrderChnage(id: number) {
    this.salesOrderIdList.push(id);
    this.getInitData();
  }

  /**
   * Get Sales Order
   * 
   * <AUTHOR>
   */
  private getSalesOrderDetail(isApiCountCheck: boolean) {
    this.setLoadingStatus(true);
    this.salesOrderApiCallService.getSalesOrderDetails(this.salesOrderId)?.subscribe({
      next: (res: HttpResponse<SalesOrderDetailResponse>) => {
        if (res.status == 200) {
          this.salesOrderDetail = res.body;
          if (!isNullOrUndefined(res.body.product)) {
            this.initializeProductData(res.body.product);
            this.setTransferDetails();
          }
          this.apiCountLoadingStatus(isApiCountCheck);
        } else {
          this.setLoadingStatus(false);
          this.toster.info(SALES_ORDER_DELETED_OR_USER_DEFINED);
          this.back();
        }
      },
      error: (error: HttpErrorResponse) => {
        this.apiCountLoadingStatus(isApiCountCheck);
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
  * Set transfer details
  * 
  * <AUTHOR>
  */
  private setTransferDetails(): void {
    this.transferSalesOrder = this.salesOrderDetail?.orderRecordType?.includes(this.tranferOrder);
    const status = this.productConfigStatus[this.salesOrderDetail.soStatus];
    const invalidStatuses = [
      this.productConfigStatus.NOT_CONFIGURED,
      this.productConfigStatus.TRANSFERRED,
      this.productConfigStatus.PARTIALLY_CONFIGURED
    ];
    this.salesOrderValidForTransfer = !invalidStatuses.includes(status);

    this.transferProductDetail = new TransferProductDetails(null, this.salesOrderId, null, null, null);
    this.salesOrderDetailOperations = this.commonOperationsService.accessSalesOrderOperations(false, true);

    if (!(this.transferSalesOrder && this.salesOrderValidForTransfer)) {
      this.salesOrderDetailOperations = this.salesOrderDetailOperations.filter(
        op => op !== SalesOrderOperationsEnum.TRANSFER_SALES_ORDER
      );
    }
  }

  /**
  * Initialize product data from sales order response
  * @param product - Product data from sales order response
  * <AUTHOR>
  */
  private initializeProductData(product: SalesOrderProduct): void {
    this.bridgesResponse = isNullOrUndefined(product.bridges) ? [] : product.bridges;
    const probeList = isNullOrUndefined(product.probes) ? [] : product.probes;
    this.licensesResponse = isNullOrUndefined(product.licenses) ? [] : product.licenses;
    this.parentSalesOrders = isNullOrUndefined(product.parentSalesOrders) ? [] : product.parentSalesOrders;
    this.childSalesOrders = isNullOrUndefined(product.childSalesOrders) ? [] : product.childSalesOrders;
    this.ProductResponseDataset(probeList);
  }

  /**
   * Sales Order Related Product data set
   *
   * <AUTHOR>
   *
   * @param salesOrderProductPageResponse
   */
  private ProductResponseDataset(salesOrderProductPageResponse: Array<SalesOrderProbeDetailResponse>): void {
    this.relatedProductList = salesOrderProductPageResponse;
    this.setFormControlAndProbe();
  }

  /**
   * Prepare Probe Feature List with enable,start date,end date
   * 
   * <AUTHOR>
   * 
   * @param salesOrderProduct 
   * @returns 
   */
  private getProbeWithFeature(salesOrderProduct: SalesOrderProbeDetailResponse): ProbeRequest {
    let probeTypeEnum = this.commonsService.getEnumKey(ProbeTypeEnum, salesOrderProduct.probeType);
    return new ProbeRequest(salesOrderProduct.enableFeatures, salesOrderProduct.enablePresets, salesOrderProduct.reminder, null, probeTypeEnum, salesOrderProduct.sopmId);
  }

  /**
   * Get Form Control
   * 
   * <AUTHOR>
   * @param index 
   * @returns 
   */
  private getFormControl(index: number): FormControl {
    return this.formGroup.get('serialNumberList')['controls'][index].get('serialNumber') as FormControl
  }

  /**
   * Create Sales order Form Control and Prepare Probe with feature
   *  
   * <AUTHOR>
   */
  private setFormControlAndProbe() {
    let formArray = this.formGroup.get("serialNumberList") as FormArray;
    formArray.clear();

    for (let index in this.relatedProductList) {
      this.relatedProductList[index].probe = this.getProbeWithFeature(this.relatedProductList[index]);
      this.relatedProductList[index].isEditMode = false;
      this.relatedProductList[index].apiErrorMessage = null;
      //Form
      if (isNullOrUndefined(this.relatedProductList[index].entityStatus) || ProductConfigStatus[this.relatedProductList[index].entityStatus] == ProductConfigStatus.NOT_CONFIGURED) {
        let serialNumberMapping = this.localSerialNumberMapping.filter(obj => obj.id == this.relatedProductList[index].sopmId);
        let value = '';
        if (serialNumberMapping.length == 1) {
          value = serialNumberMapping[0].probe.serialNumber;
          this.relatedProductList[index].isEditMode = true;
        }
        formArray.push(new FormGroup({
          serialNumber: new FormControl(value, [this.requiredValidator,
          this.commonsService.removeSpacesThrowError(),
          this.validationService.cannotContainSpace,
          Validators.maxLength(Small_TextBoxMaxLength),
          Validators.pattern(SERIAL_NUMBER_PATTERN),
          this.distinctSerialNumberValidate(this, index)]),
        }));
      } else {
        formArray.push(new FormGroup({
          serialNumber: new FormControl(this.relatedProductList[index].entitySerialNumber, [])
        }));
        //Remove Local Serial Number mapping
        this.removeLocolSerialNumberMapping(this.relatedProductList[index].sopmId);
      }
    }

    //error message display
    for (let index in this.formGroup.get('serialNumberList')['controls']) {
      let control = this.getFormControl(parseInt(index));
      if (!isNullOrUndefined(control?.errors) && !(this.commonsService.checkValueIsNullOrEmpty(control.value))) {
        control.markAllAsTouched();
      }
    }
  }

  /**
   * All Api Response After Loading Closed
   * 
   * <AUTHOR>
   */
  private apiCountLoadingStatus(isApiCountCheck: boolean): void {
    if (isApiCountCheck) {
      this.apiResponseCount++;
      if (this.totelApiCount == this.apiResponseCount) {
        this.setLoadingStatus(false);
        this.apiResponseCount = 0;
      }
    } else {
      this.setLoadingStatus(false);
    }
  }

  /**
  * Set Loading 
  * 
  * <AUTHOR>
  * 
  * @param status 
  */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
  }

  /**
   * Serial Number Edit Mode on
   * 
   * <AUTHOR>
   * 
   * @param index 
   */
  public editSerialNumber(id: number, index: number): void {
    this.relatedProductList[index].isEditMode = true;
    this.localSerialNumberMapping.push(new SerialNumberMapping(id, this.relatedProductList[index].probe));
    this.setButtonDisabledStatus();
  }

  /**
   * Input serial Number and Update Local Data
   * 
   * <AUTHOR>
   * 
   * @param index 
   * @param id 
   * @param serialNumber 
   */
  public setSerailNumberLocalData(index: number, id: number, serialNumberValue: string): void {
    let serialNumberMappingIndex = this.localSerialNumberMapping.findIndex(obj => obj.id == id);
    if (serialNumberMappingIndex != -1) {
      let serialNumber = this.commonsService.checkNullFieldValue(serialNumberValue);
      this.getFormControl(index).setValue(serialNumber);
      //API Error message hide
      this.relatedProductList[index].apiErrorMessage = null;
      this.relatedProductList[index].entitySerialNumber = serialNumber;
      this.localSerialNumberMapping[serialNumberMappingIndex].probe.serialNumber = this.probeService.truncateSerialNumber(serialNumber);
    }
    this.otherSerialNumberValidate(index);
  }

  /**
  * Sales Order Detail Operations
  * @param event 
  */
  public changeSalesOrderOperation(event): void {
    switch (event.target.value) {
      case SalesOrderOperationsEnum.DELETE_ORDER:
        this.deleteSalesOrder();
        break
      case SalesOrderOperationsEnum.TRANSFER_SALES_ORDER:
        this.transferOrderSelectionToggle(false, true);
        break;
      default:
        break;
    }
    let selection = document.getElementById('salesOrderDetailOperation') as HTMLSelectElement;
    selection.value = SalesOrderOperationsEnum.SALES_ORDER_OPERATIONS;
  }

  /**
  * <AUTHOR>
  * @returns {Promise<void>}
  * @description This function downloads the PDF letter for the sales order.
  */
  public async downloadSalesOrderPdfLetter(): Promise<void> {
    try {
      this.loading = true;
      const response: HttpResponse<SalesOrderPdfLetterResponse> = await lastValueFrom(this.salesOrderApiCallService.downloadSalesOrderPdfLetter(this.salesOrderDetail.salesOrderNumber));
      await this.downloadService.downloadMyFile(response.body.pdfDownloadURL);
    } catch (error) {
      this.exceptionService.customErrorMessage(error);
    } finally {
      this.loading = false;
    }
  }


  /**
   * Check Dupicate Serial Number
   * 
   * <AUTHOR>
   * 
   * @param componentVariable 
   * @param controlIndex 
   * @returns 
   */
  public distinctSerialNumberValidate(componentVariable: any, controlIndex: any): ValidatorFn {
    return function validate(control: FormControl) {
      if (!isNullOrUndefined(control) && !isNullOrUndefined(control.value) && (typeof control.value == "string" && control.value.replace(/\s/g, '').length > 0)) {
        let controlValue: any = control.value;
        let index = controlValue.indexOf(controlValue.match(Find_Letter_Pattern));
        //Get Probe List in probeFeatureRequest (local data)
        let allSerialNumberList = componentVariable.formGroup.get("serialNumberList").value;
        if (!isNullOrUndefined(allSerialNumberList)) {
          allSerialNumberList.splice(controlIndex, 1);
        }
        let filterData = componentVariable.distinctSerialNumberFilter(allSerialNumberList, control, index);
        if (filterData.length > 0) {
          return { serialNumberexists: true };
        }
      }
      return null;
    };
  }

  public distinctSerialNumberFilter(allSerialNumberList: any, control: FormControl, index: number): Array<any> {
    return allSerialNumberList.filter(obj => {
      if (!isNullOrUndefined(obj.serialNumber)) {
        let indexForSerialNumber = obj.serialNumber.indexOf(obj.serialNumber.match(Find_Letter_Pattern));
        if (obj.serialNumber.substr(indexForSerialNumber).toLowerCase() == control.value.substr(index).toLowerCase()) {
          return obj;
        }
      }
    });
  }

  /**
   * Find Duplicate Serial Number
   * 
   * @returns 
   */
  private duplicateSerialNumberValue(): Array<string> {
    let duplicateValue: Array<string> = [];
    let serialNumberList: Array<string> = [];
    for (let product of this.relatedProductList) {
      let serialNumber = this.probeService.truncateSerialNumber(product.probe.serialNumber);
      if (!isNullOrUndefined(serialNumber)) {
        if (serialNumberList.includes(serialNumber) && !duplicateValue.includes(serialNumber)) {
          duplicateValue.push(serialNumber);
        } else {
          serialNumberList.push(serialNumber);
        }
      }
    }
    return duplicateValue;
  }

  /**
   * Other TextBox Serial Number Validate if invalid then set error
   * 
   * <AUTHOR>
   * 
   * @param currentIndex 
   */
  private otherSerialNumberValidate(currentIndex: number): void {
    let duplicateSerialNumberList = this.duplicateSerialNumberValue();
    for (let index in this.relatedProductList) {
      if (currentIndex != parseInt(index) && this.relatedProductList[index].isEditMode) {
        let control = this.getFormControl(parseInt(index));
        if (!isNullOrUndefined(control) && !(this.commonsService.checkValueIsNullOrEmpty(control.value))) {
          this.updateFormControlvalidation(duplicateSerialNumberList, control, parseInt(index));
        }
      }
    }
    this.setButtonDisabledStatus();
  }

  /**
   * Update Form Control validations
   * 
   * <AUTHOR>
   * @param duplicateSerialNumberList 
   * @param control 
   */
  private updateFormControlvalidation(duplicateSerialNumberList: string[], control: FormControl, relatedProductListIndex: number) {
    let errorList = control.errors;
    if (duplicateSerialNumberList.includes(this.probeService.truncateSerialNumber(control.value))) {
      control.setErrors(this.getcombileErrorList(errorList));
      //API Error message hide
      this.relatedProductList[relatedProductListIndex].apiErrorMessage = null;
    } else if (!isNullOrUndefined(errorList) && !isNullOrUndefined(errorList['serialNumberexists'])) {
      delete errorList["serialNumberexists"];
      let error = (Object.keys(errorList).length === 0) ? null : errorList;
      control.setErrors(error);
    }
  }

  /**
   * Add a error object or create a new error object
   * 
   * <AUTHOR>
   * @param errorList 
   * @returns 
   */
  private getcombileErrorList(errorList: any): any {
    let combileErrorList = errorList;
    if (isNullOrUndefined(combileErrorList)) {
      //New error object create
      combileErrorList = { serialNumberexists: true };
    } else {
      //add new error in error list
      combileErrorList['serialNumberexists'] = true;
    }
    return combileErrorList;
  }

  private setButtonDisabledStatus(): void {
    let controlStatusList: Array<boolean> = [];
    for (let index in this.relatedProductList) {
      if (this.relatedProductList[index].isEditMode) {
        let control = this.getFormControl(parseInt(index));
        if (control.invalid) {
          controlStatusList.push(false);
        }
      }
    }
    this.formIsValid = (controlStatusList.length == 0);
  }

  /**
   * Close Single Edit Mode 
   * 
   * @param index 
   * @param id 
   */
  public closeSingleEditMode(index: number, id: number): void {
    let control = this.getFormControl(index);
    if (!isNullOrUndefined(control)) {
      control.setValue(null);
      control.setErrors({ required: true });
      control.markAsPristine();
      control.markAsUntouched();
    }
    this.relatedProductList[index].isEditMode = false;
    this.relatedProductList[index].apiErrorMessage = null;
    this.relatedProductList[index].entitySerialNumber = null;
    this.removeLocolSerialNumberMapping(id);
    this.setButtonDisabledStatus();
  }


  /**
   * Save single Serial number
   * 
   * <AUTHOR>
   * 
   * @param index 
   */
  public saveSerialNumber(index: number, id: number): void {
    let control = this.getFormControl(index);
    if (control.valid) {
      let serialNumberMapping: SerialNumberMapping = this.localSerialNumberMapping.filter(obj => obj.id == id)[0];
      this.prepareCreateProbeRequest([serialNumberMapping]);
    } else {
      control.markAllAsTouched();
      this.toster.info("Invalid Serial Number");
    }
  }
  /**
   * save Multiple serial number
   * 
   * <AUTHOR>
   */
  public saveMultipleSerialNumber(): void {
    if (this.formIsValid) {
      this.prepareCreateProbeRequest(this.localSerialNumberMapping);
    } else {
      this.toster.info("Invalid Serial Number");
    }
  }

  /**
   * Close Edit Mode 
   * 
   * <AUTHOR>
   */
  public closeEditMode(): void {
    for (let index in this.relatedProductList) {
      if (isNullOrUndefined(this.relatedProductList[index].entityStatus) || ProductConfigStatus[this.relatedProductList[index].entityStatus] == ProductConfigStatus.NOT_CONFIGURED) {
        this.relatedProductList[index].isEditMode = false;
        this.relatedProductList[index].apiErrorMessage = null;
        this.relatedProductList[index].entitySerialNumber = null;
      }
    }
    this.localSerialNumberMapping = [];
    this.setFormControlAndProbe();
    this.setButtonDisabledStatus();
  }

  /**
   * Prepare Create Probe Request
   * 
   * <AUTHOR>
   * @param serialNumberMapping 
   */
  private prepareCreateProbeRequest(serialNumberMapping: Array<SerialNumberMapping>) {
    let probeRequestList: Array<ProbeRequest> = [];
    for (let salesOrderProduct of serialNumberMapping) {
      probeRequestList.push(salesOrderProduct.probe);
    }
    const orderRecordType = this.salesOrderDetail?.orderRecordType ?? null;
    let probeFeatureRequest = new ProbeFeatureRequest(this.salesOrderDetail?.salesOrderNumber,
      this.salesOrderDetail?.customerName,
      this.salesOrderDetail?.customerEmail, this.salesOrderDetail?.countryId, this.salesOrderDetail?.poNumber, orderRecordType, probeRequestList, false, this.salesOrderDetail?.deviceAutoLock, this.salesOrderDetail?.probeAutoLock);
    this.callMultiprobeSaveAPI(probeFeatureRequest);
  }


  /**
   * Create Probe Api Call After sales order with product mapping api call
   * 
   * <AUTHOR>
   *  
   * @param finalProbeFeatureRequest 
   */
  public callMultiprobeSaveAPI(finalProbeFeatureRequest: ProbeFeatureRequest): void {
    this.setLoadingStatus(true);
    this.probeApiService.saveMutiprobe(finalProbeFeatureRequest)?.subscribe(
      {
        next: (response: HttpResponse<AddUpdateMultiProbeResponse>) => {
          if (response.body.error) {
            this.createAndUpdateProbeErrorShow(response.body.probes);
            this.setLoadingStatus(false);
          } else {
            this.setSalesOrderProductMappingResponse(response.body.probes);
          }
        },
        error: (error: HttpErrorResponse) => {
          this.setLoadingStatus(false);
          if (error.status == 409) {
            if (error.error["errorMessage"].includes(VIOLATION_UNIQUE_KEY_CONSTRAINT)) {
              this.toster.error(PROBE_ALREADY_EXIEST);
            } else {
              this.toster.info(error.error["errorMessage"]);
            }
          } else if (error.status == 412) {
            this.toster.info(error.error["errorMessage"]);
          } else {
            this.getSalesOrderDetail(true);
            this.exceptionService.customErrorMessage(error);
          }
        }
      });
  }

  /**
   * API Error Message Show
   * 
   * @param multipleProbeResponse 
   */
  private createAndUpdateProbeErrorShow(multipleProbeResponse: Array<MultipleProbeResponse>) {
    for (let probe of multipleProbeResponse) {
      let localSerialNumberMapping: Array<SerialNumberMapping> = this.localSerialNumberMapping.filter(obj => obj.probe.serialNumber == probe.serialNumber);
      if (localSerialNumberMapping.length == 1) {
        let index = this.relatedProductList.findIndex(probeObj => probeObj.sopmId == localSerialNumberMapping[0].id);
        if (index != -1) {
          this.relatedProductList[index].apiErrorMessage = probe.errorMessage;
        }
      }
    }
  }

  /**
   * Sales Order With Mapping Product Id (Probe Id Or Device Id Etc)
   * 
   * <AUTHOR>
   * @param SalesOrderProductMappingResponse 
   */
  private setSalesOrderProductMappingResponse(multipleProbeResponse: Array<MultipleProbeResponse>): void {
    for (let salesOrderProduct of multipleProbeResponse) {
      this.removeLocolSerialNumberMapping(salesOrderProduct.probeId);
    }
    this.toster.success(ProbeSuccessMessage);
    this.getSalesOrderDetail(true);
  }

  /**
   * Remove From Locol Serial Number Mapping
   * 
   * <AUTHOR>
   * @param salesOrderProductId 
   */
  private removeLocolSerialNumberMapping(salesOrderProductId: number) {
    let localSerialNumberMappingIndex = this.localSerialNumberMapping.findIndex(obj => obj.id == salesOrderProductId);
    if (localSerialNumberMappingIndex != -1) {
      this.localSerialNumberMapping.splice(localSerialNumberMappingIndex, 1);
    }
  }

  /**
   * Sales Order Listing page
   * 
   * <AUTHOR>
   */
  public back(): void {
    if (this.salesOrderIdList.length > 0) {
      this.salesOrderIdList.pop();

      // If there are still items in the list, reload data for the previous sales order
      // Otherwise, navigate back to the sales order list
      if (this.salesOrderIdList.length > 0) {
        this.getInitData();
      } else {
        this.showSalesOrderList.emit();
      }
    }
  }

  /**
   * Show Sales Order Detail Page And Hide Probe Detail Page or kit detail page
   * 
   * <AUTHOR>
   */
  public showSalesOrderDetailPage(): void {
    this.detailPageToggle(null, null, null, true, false, false);
    this.getInitData();
  }

  /**
   * Show Probe Detail Page And Hide Sales Order Detail page
   * 
   * @param productEntityId 
   */
  public showProbeDetailPage(productEntityId: number): void {
    this.closeEditMode();
    this.detailPageToggle(productEntityId, null, null, false, true, false);
  }

  public showDeviceDetailPage(productEntityId: number): void {
    this.detailPageToggle(null, productEntityId, null, false, false, true);
  }

  public detailPageToggle(productEntityId: number, productEntityValue: number,
    salesOrderPartNumberMappingId: number, isSalesOrderDetailPageDiplay: boolean,
    isProbeDetailPageDiplay: boolean,
    isDeviceDetailPageDiplay: boolean): void {
    this.productEntityId = productEntityId;
    this.productEntityValue = productEntityValue;
    this.salesOrderPartNumberMappingId = salesOrderPartNumberMappingId;
    this.salesOrderDetailPageDiplay = isSalesOrderDetailPageDiplay;
    this.probeDetailPageDiplay = isProbeDetailPageDiplay;
    this.deviceDetailPageDiplay = isDeviceDetailPageDiplay;
  }

  /**
  * Toggles the display states for sales order details and transfer order sections.
  * 
  * <AUTHOR>
  * 
  * @param salesOrderDetailDisplay 
  * @param tranferOrderSectionDisplay
  */
  public transferOrderSelectionToggle(salesOrderDetailDisplay: boolean, tranferOrderSectionDisplay: boolean) {
    this.transferOrderSelectionDisaplay = tranferOrderSectionDisplay;
    this.salesOrderDetailPageDiplay = salesOrderDetailDisplay;
    if (salesOrderDetailDisplay) this.getInitData();
  }

  /**
   * Call Reset Bridge API
   * 
   * <AUTHOR>
   * @param index 
   * @param $sopmId 
   */
  public async resetBridge(index: number, $sopmId: number) {
    this.setLoadingStatus(true);
    let salesOrderProductMappingRequest = new SalesOrderProductMappingRequest($sopmId);
    let resetBridgeResponse: SalesOrderProductMappingResponse = await this.salesOrderApiCallService.resetSalesOrderBridge(salesOrderProductMappingRequest, this.salesOrderId);
    if (!isNullOrUndefined(resetBridgeResponse) && resetBridgeResponse.sopmId == $sopmId) {
      this.bridgesResponse[index] = this.salesOrderApiCallService.updateBridgeInfo(this.bridgesResponse[index], resetBridgeResponse, false);
      this.setLoadingStatus(false);
    } else {
      this.getInitData();
    }
  }
  /**
  *  Reload the page
  */
  public refreshFilter() {
    this.getSalesOrderDetail(true);
  }

  /**
 * Delete Sales Order
 *
 * <AUTHOR>
 */
  public deleteSalesOrder(): void {
    this.dialogservice.confirm('Delete', DELETE_SALES_ORDER_CONFIRMATION_MESSAGE)
      .then((confirmed) => {
        if (confirmed) {
          this.setLoadingStatus(true);
          this.salesOrderApiCallService.deleteSalesOrder([this.salesOrderId])?.subscribe({
            next: (res: HttpResponse<SuccessMessageResponse>) => {
              this.toster.success(res.body.message);
              this.back();
            },
            error: (error: HttpErrorResponse) => {
              this.setLoadingStatus(false);
              this.exceptionService.customErrorMessage(error);
            }
          })
        }
      });
  }

}
