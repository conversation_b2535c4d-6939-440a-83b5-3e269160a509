import { HttpErrorResponse } from '@angular/common/http';
import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Medium_TextBoxMaxCharactersAllowedMessage, Medium_TextBoxMaxLength, Small_TextBoxMaxCharactersAllowedMessage, SPECIAL_CHARACTER_PATTERN, SpecialCharacterErrorMessage, TextAreaMaxCharactersAllowedMessage, TextAreaMaxLength, UpdateJsonSuccessMessage } from 'src/app/app.constants';
import { BasicModelConfig } from 'src/app/model/common/BasicModelConfig.model';
import { CreateJsonRequest } from 'src/app/model/video/create-json-request.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { SubjectMessageService } from 'src/app/shared/subject-message.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { VideoService } from 'src/app/shared/videoservice/video.service';

@Component({
  selector: 'app-upload-json-dialog',
  templateUrl: './upload-json-dialog.component.html',
  styleUrls: ['./upload-json-dialog.component.css']
})
export class UploadJsonDialogComponent implements OnInit {

  @Input() basicModelConfig: BasicModelConfig;
  @Input() jsonFileObj: any;
  @Input() videoIds: number[];
  @Input() btnBacktoSelection: string;

  jsonId: number = null;

  disableBtn = true;
  jsonFileValidation = false;
  disabled = false;

  textBoxMaxCharactersAllowedMessage: string = Medium_TextBoxMaxCharactersAllowedMessage;
  textBoxMinCharactersAllowedMessage: string = Small_TextBoxMaxCharactersAllowedMessage;
  textAreaMaxLengthMessage: string = TextAreaMaxCharactersAllowedMessage;
  specialCharacterErrorMessage: string = SpecialCharacterErrorMessage;

  createJSONForm = new FormGroup({
    Title: new FormControl('', [Validators.required, Validators.maxLength(Medium_TextBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    version: new FormControl('', [Validators.required, this.commentservice.validateJsonInput]),
    notes: new FormControl('', [Validators.maxLength(TextAreaMaxLength)])
  });

  constructor(
    private activeModal: NgbActiveModal,
    private videoService: VideoService,
    private exceptionService: ExceptionHandlingService,
    private toastrService: ToastrService,
    private subjectMessageService: SubjectMessageService,
    private commentservice: CommonsService
  ) { }

  ngOnInit() {
    if (!isNullOrUndefined(this.jsonFileObj)) {
      this.jsonId = this.jsonFileObj.jsonId;
      this.setValueToForm();
    }
  }

  public setValueToForm() {
    this.createJSONForm.get('Title').setValue(this.jsonFileObj.title);
    this.createJSONForm.get('version').setValue(this.jsonFileObj.name);
    this.createJSONForm.get('notes').setValue(this.jsonFileObj.notes);
    if (this.jsonId != null) {
      this.createJSONForm.get('Title').disable();
      this.createJSONForm.get('version').disable();
    }
  }

  public getCreateJSONDialogInformation(createJSONForm) {
    return new CreateJsonRequest(createJSONForm.Title, createJSONForm.version, createJSONForm.notes, this.videoIds);
  }

  public getUpdateJSONDialogInformation(updateJSONForm) {
    return new CreateJsonRequest(this.createJSONForm.get('Title').value, this.createJSONForm.get('version').value, updateJSONForm.notes, this.videoIds);
  }

  public accept() {
    this.setLoadingStatus(true);
    if (this.jsonId == null) {
      let createJSONDialogInformation: CreateJsonRequest = this.getCreateJSONDialogInformation(this.createJSONForm.value);
      this.videoService.createJson(createJSONDialogInformation).subscribe({
        next: (response) => {
          this.setLoadingStatus(false);
          this.toastrService.success(response.body?.message);
          this.activeModal.close(true);
        }, error: (error: HttpErrorResponse) => {
          this.setLoadingStatus(false);
          this.exceptionService.customErrorMessage(error);
        }
      });
    } else if (this.jsonId != null) {
      let updateJSONDialogInformation: CreateJsonRequest = this.getUpdateJSONDialogInformation(this.createJSONForm.value);
      this.videoService.updateJson(updateJSONDialogInformation, this.jsonId).subscribe({
        next: (response) => {
          this.setLoadingStatus(false);
          this.toastrService.success(UpdateJsonSuccessMessage);
          this.activeModal.close(true);
        }, error: (error: HttpErrorResponse) => {
          this.setLoadingStatus(false);
          this.exceptionService.customErrorMessage(error);
        }
      });
    }
  }

  /**
   * Set Loading Status
   * 
   * <AUTHOR>
   * @param status 
   */
  private setLoadingStatus(status: boolean): void {
    this.subjectMessageService.setLoading(status);
  }

  public decline() {
    this.activeModal.close(false);
  }

  public dismiss() {
    this.activeModal.dismiss();
  }
}
