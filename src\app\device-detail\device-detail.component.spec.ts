import { DatePipe } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { commonsProviders } from '../Tesing-Helper/test-utils';
import { ConfirmDialogService } from '../confirmationdialog/confirmation.service';
import { ExceptionHandlingService } from '../shared/ExceptionHandling.service';
import { RoleApiCallService } from '../shared/Service/RoleService/role-api-call.service';
import { SSOLoginService } from '../shared/Service/SSO/ssologin.service';
import { AuthJwtService } from '../shared/auth-jwt.service';
import { DeviceService } from '../shared/device.service';
import { BooleanKeyValueMappingDisplayNamePipe } from '../shared/pipes/Common/BooleanKeyValueMappingDisplayNamePipe.pipe';
import { EnumMappingDisplayNamePipe } from '../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { HidePermissionNamePipe } from '../shared/pipes/Role/hidePermissionName.pipe';
import { DeviceTypeNamePipe } from '../shared/pipes/device-type-name.pipe';
import { PrintListPipe } from '../shared/pipes/printList.pipe';
import { CommonOperationsService } from '../shared/util/common-operations.service';
import { CommonsService } from '../shared/util/commons.service';
import { DownloadService } from '../shared/util/download.service';
import { KeyValueMappingServiceService } from '../shared/util/key-value-mapping-service.service';
import { DeviceDetailComponent } from './device-detail.component';
import { TransferOrderModuleComponent } from '../FeatureModule/TransferOrder/transfer-order-module/transfer-order-module.component';

describe('DeviceDetailComponent', () => {
  let component: DeviceDetailComponent;
  let fixture: ComponentFixture<DeviceDetailComponent>;

  beforeEach(async () => {
    const toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);

    await TestBed.configureTestingModule({
      declarations: [DeviceDetailComponent, TransferOrderModuleComponent, EnumMappingDisplayNamePipe, DeviceTypeNamePipe, BooleanKeyValueMappingDisplayNamePipe],
      imports: [],
      providers: [DeviceService,
        DownloadService,
        ConfirmDialogService,
        CommonsService,
        LocalStorageService,
        DatePipe,
        ExceptionHandlingService,
        AuthJwtService,
        SessionStorageService,
        SSOLoginService,
        CommonOperationsService,
        RoleApiCallService,
        HidePermissionNamePipe,
        KeyValueMappingServiceService,
        PrintListPipe,
        EnumMappingDisplayNamePipe,
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeviceDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
