import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { isUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { SPECIAL_CHARACTER_PATTERN, SpecialCharacterErrorMessage, Small_TextBoxMaxCharactersAllowedMessage, Small_TextBoxMaxLength } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { KitManagemantFilterAction } from 'src/app/model/KitManagement/KitManagemantFilterAction.model';
import { KitManagemantSearchRequestBody } from 'src/app/model/KitManagement/KitManagemantSearchRequestBody.model';
import { LanguageResponse } from 'src/app/model/Languages/LanguageResponse.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { CountryAndLanguageService } from 'src/app/shared/Service/CountryAndLanguageService/country-and-language.service';
import { KitManagemantService } from 'src/app/shared/Service/KitManagemant/kit-managemant.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { ValidationService } from 'src/app/shared/util/validation.service';

@Component({
  selector: 'app-kit-management-filter',
  templateUrl: './kit-management-filter.component.html',
  styleUrls: ['./kit-management-filter.component.css']
})
export class KitManagementFilterComponent implements OnInit {

  @Input("isFilterComponentInitWithApicall") isFilterComponentInitWithApicall: boolean;
  @Input("listPageRefreshForbackToDetailPage") listPageRefreshForbackToDetailPage: boolean;
  @Input("kitManagemantSearchRequestBody") kitManagemantSearchRequestBody: KitManagemantSearchRequestBody;

  //MaxLength Message
  textBoxMaxLengthMessage: string = Small_TextBoxMaxCharactersAllowedMessage;
  specialCharacterErrorMessage: string = SpecialCharacterErrorMessage;


  filterKitForm = new FormGroup({
    kitPartNumber: new FormControl(null, [Validators.maxLength(Small_TextBoxMaxLength), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    bridgePartNumber: new FormControl(null, [Validators.maxLength(Small_TextBoxMaxLength), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    countries: new FormControl([], []),
    softwareLanguage: new FormControl([], []),
    videoVersion: new FormControl(null, [Validators.maxLength(Small_TextBoxMaxLength), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    buildVersion: new FormControl(null, [Validators.maxLength(Small_TextBoxMaxLength), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
  });

  subscriptionForRefeshList: Subscription;

  defaultListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

  countrySetting: MultiSelectDropdownSettings = null;
  languagesSetting: MultiSelectDropdownSettings = null;
  countriesList: CountryListResponse[] = [];
  languagesList: LanguageResponse[] = [];

  constructor(private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private countryAndLanguageService: CountryAndLanguageService,
    private kitManagemantService: KitManagemantService,
    private commonsService: CommonsService,
    private commonOperationsService: CommonOperationsService,
    private validationService: ValidationService,
    private countryCacheService: CountryCacheService,
  ) { }


  /**
    * On Init 
    * 
    * <AUTHOR>
    */
  public ngOnInit(): void {
    this.countrySetting = this.multiSelectDropDownSettingService.getCountryDrpSetting(false, false);
    this.languagesSetting = this.multiSelectDropDownSettingService.getLanguageDrpSetting();
    this.onInitSubject();
    this.getFilterList();
    if (this.isFilterComponentInitWithApicall) {
      this.clearFilter(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
   * Destroy 
   */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForRefeshList)) { this.subscriptionForRefeshList.unsubscribe() }
  }

  /**
   * Subject 
   * 
   * <AUTHOR>
   */
  public onInitSubject(): void {
    /**
     * Kit Refresh After some Action 
     * <AUTHOR>
     */
    this.subscriptionForRefeshList = this.kitManagemantService.getKitListRefreshSubject().subscribe((listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
      if (listingPageReloadSubjectParameter.isReloadData) {
        if (listingPageReloadSubjectParameter.isClearFilter) {
          this.clearFilter(listingPageReloadSubjectParameter);
        } else {
          //page change 1,2,3
          this.kitListPageRefresh(listingPageReloadSubjectParameter);
        }
      }
    });
  }


  /**
  * Get Filter Value List
  *
  * <AUTHOR>
  */
  public async getFilterList(): Promise<void> {
    if (this.kitManagemantService.getCountryList().length == 0 || this.kitManagemantService.getLanguageList().length == 0) {
      this.countriesList = await this.countryCacheService.getCountryListFromCache();
      this.languagesList = await this.countryAndLanguageService.getLanguageList();
      this.kitManagemantService.setCountryList(this.countriesList);
      this.kitManagemantService.setLanguageList(this.languagesList);
    } else {
      this.countriesList = this.kitManagemantService.getCountryList();
      this.languagesList = this.kitManagemantService.getLanguageList();
    }
    this.setFilterValue();
  }

  /**
   * set Filter value
   */
  private setFilterValue() {
    if (this.kitManagemantSearchRequestBody != null) {
      this.filterKitForm.get('kitPartNumber').setValue(this.kitManagemantSearchRequestBody.kitPartNumber);
      this.filterKitForm.get('bridgePartNumber').setValue(this.kitManagemantSearchRequestBody.bridgePartNumber);
      this.filterKitForm.get('videoVersion').setValue(this.kitManagemantSearchRequestBody.videoVersion);
      this.filterKitForm.get('buildVersion').setValue(this.kitManagemantSearchRequestBody.softWareVersion);
      this.filterKitForm.get('countries').setValue(this.commonsService.getDropDownValue(this.countriesList, this.kitManagemantSearchRequestBody.countryIds));
      this.filterKitForm.get('softwareLanguage').setValue(this.commonsService.getDropDownValue(this.languagesList, this.kitManagemantSearchRequestBody.languageIds));
    }
    if (this.listPageRefreshForbackToDetailPage) {
      this.kitListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }


  /**
   * Search Data
   * 
   * <AUTHOR>
   */
  public searchData(): void {
    let allFormValue = this.filterKitForm.value;
    this.filterKitForm.get('kitPartNumber').setValue(this.commonsService.checkNullFieldValue(allFormValue.kitPartNumber));
    this.filterKitForm.get('bridgePartNumber').setValue(this.commonsService.checkNullFieldValue(allFormValue.bridgePartNumber));
    this.filterKitForm.get('videoVersion').setValue(this.commonsService.checkNullFieldValue(allFormValue.videoVersion));
    this.filterKitForm.get('buildVersion').setValue(this.commonsService.checkNullFieldValue(allFormValue.buildVersion));

    if (this.filterKitForm.invalid || (this.commonsService.checkValueIsNullOrEmpty(allFormValue.kitPartNumber) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.bridgePartNumber) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.countries) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.softwareLanguage) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.videoVersion) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.buildVersion))) {
      this.commonOperationsService.showEmptyFilterTosteMessge();
    } else {
      this.kitListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
   * Clear All filter and serach api call
   * 
   * <AUTHOR>
   * 
   * @param listingPageReloadSubjectParameter 
   */
  public clearFilter(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    this.filterKitForm.get('kitPartNumber').setValue(null);
    this.filterKitForm.get('bridgePartNumber').setValue(null);
    this.filterKitForm.get('videoVersion').setValue(null);
    this.filterKitForm.get('buildVersion').setValue(null);
    this.filterKitForm.get('countries').setValue([]);
    this.filterKitForm.get('softwareLanguage').setValue([]);
    this.kitListPageRefresh(listingPageReloadSubjectParameter);
  }

  /**
   * Kit List Page Serch api call
   * 
   * <AUTHOR>
   * 
   * @param listingPageReloadSubjectParameter 
   */
  private kitListPageRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    if (this.filterKitForm.invalid) {
      this.filterKitForm.reset();
    }
    let allFormValue = this.filterKitForm.value;
    let countryIdList = this.commonsService.getIdsFromArray(allFormValue.countries);
    let languageIdList = this.commonsService.getIdsFromArray(allFormValue.softwareLanguage);
    let kitRequestBody = new KitManagemantSearchRequestBody(
      this.commonsService.checkNullFieldValue(allFormValue.kitPartNumber),
      this.commonsService.checkNullFieldValue(allFormValue.bridgePartNumber),
      countryIdList, languageIdList,
      this.commonsService.checkNullFieldValue(allFormValue.videoVersion),
      this.commonsService.checkNullFieldValue(allFormValue.buildVersion));
    let roleFilterAction = new KitManagemantFilterAction(listingPageReloadSubjectParameter, kitRequestBody);
    this.kitManagemantService.callKitListFilterRequestParameterSubject(roleFilterAction);
  }

}
