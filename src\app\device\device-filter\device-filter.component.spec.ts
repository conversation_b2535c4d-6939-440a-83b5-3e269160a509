import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService } from 'ngx-webstorage';
import { of, Subject, throwError } from 'rxjs';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ClientDevice, DemoDevice, TestDevice } from '../../app.constants';
import { ListingPageReloadSubjectParameter } from '../../model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from '../../model/Country/CountryListResponse.model';
import { DeviceSearchRequest } from '../../model/device/deviceSearchRequest.model';
import { DeviceService } from '../../shared/device.service';
import { ProductStatusEnum } from '../../shared/enum/Common/ProductStatus.enum';
import { ExceptionHandlingService } from '../../shared/ExceptionHandling.service';
import { CountryCacheService } from '../../shared/Service/CacheService/countrycache.service';
import { DeviceOperationService } from '../../shared/Service/DeviceService/device-operation.service';
import { SalesOrderApiCallService } from '../../shared/Service/SalesOrderService/sales-order-api-call.service';
import { CommonsService } from '../../shared/util/commons.service';
import { KeyValueMappingServiceService } from '../../shared/util/key-value-mapping-service.service';
import { MultiSelectDropDownSettingService } from '../../shared/util/multi-select-drop-down-setting.service';
import { DeviceFilterComponent } from './device-filter.component';

describe('DeviceFilterComponent', () => {
  let component: DeviceFilterComponent;
  let fixture: ComponentFixture<DeviceFilterComponent>;
  let deviceFilterService: jasmine.SpyObj<DeviceOperationService>;
  let deviceService: jasmine.SpyObj<DeviceService>;
  let salesOrderApiCallService: jasmine.SpyObj<SalesOrderApiCallService>;
  let countryCacheService: jasmine.SpyObj<CountryCacheService>;
  let commonsService: jasmine.SpyObj<CommonsService>;
  let commonOperationsService: jasmine.SpyObj<CommonOperationsService>;
  let exceptionService: jasmine.SpyObj<ExceptionHandlingService>;
  let multiSelectService: jasmine.SpyObj<MultiSelectDropDownSettingService>;
  let keyValueService: jasmine.SpyObj<KeyValueMappingServiceService>;
  let refreshSubject: Subject<ListingPageReloadSubjectParameter>;

  beforeEach(async () => {
    refreshSubject = new Subject<ListingPageReloadSubjectParameter>();

    const deviceFilterServiceSpy = jasmine.createSpyObj('DeviceOperationService', [
      'getDeviceListRefreshSubject',
      'getPackageVersionList',
      'getSalesOrderNumberList',
      'getCountryList',
      'setPackageVersionList',
      'setSalesOrderNumberList',
      'setCountryList',
      'callDeviceListFilterRequestParameterSubject'
    ]);
    const deviceServiceSpy = jasmine.createSpyObj('DeviceService', ['getpackageVersion']);
    const salesOrderApiCallServiceSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderNumberList']);
    const countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache']);
    const toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['info']);
    const exceptionServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    const commonsServiceSpy = jasmine.createSpyObj('CommonsService', [
      'checkForNull',
      'checkNullFieldValue',
      'checkValueIsNullOrEmpty',
      'getEnumMappingSelectedValue',
      'getIdsFromArray',
      'getSelectedValueFromEnum',
      'getSelectedValueFromBooleanKeyValueMapping',
      'getDeviceTypeStringToEnum'
    ]);
    const commonOperationsServiceSpy = jasmine.createSpyObj('CommonOperationsService', ['showEmptyFilterTosteMessge']);
    const multiSelectServiceSpy = jasmine.createSpyObj('MultiSelectDropDownSettingService', [
      'getLockStateDropdownSetting',
      'getEditStateDropdownSetting',
      'getSystemSoftwearVersionDropdownSetting',
      'getDeviceConnectionStateDropdownSetting',
      'getDeviceTypeDropdownSetting',
      'getSalesOrderNumberDrpSetting',
      'getCountryDrpSetting',
      'getProductStatusDrpSetting',
      'setOtherOptionDisabled',
      'setAllOptionEnable'
    ]);
    const keyValueServiceSpy = jasmine.createSpyObj('KeyValueMappingServiceService', [
      'enumOptionToList',
      'lockedUnlockOptionList',
      'editEnableDisableOptionList'
    ]);

    // Setup default return values
    deviceFilterServiceSpy.getDeviceListRefreshSubject.and.returnValue(refreshSubject);
    deviceFilterServiceSpy.getPackageVersionList.and.returnValue([]);
    deviceFilterServiceSpy.getSalesOrderNumberList.and.returnValue([]);
    deviceFilterServiceSpy.getCountryList.and.returnValue([]);

    await TestBed.configureTestingModule({
      declarations: [DeviceFilterComponent],
      imports: [ReactiveFormsModule],
      providers: [
        FormBuilder,
        CommonOperationsService,
        RoleApiCallService,
        ConfirmDialogService,
        PermissionService,
        LocalStorageService,
        HidePermissionNamePipe,
        { provide: DeviceOperationService, useValue: deviceFilterServiceSpy },
        { provide: DeviceService, useValue: deviceServiceSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: CommonOperationsService, useValue: commonOperationsServiceSpy },
        { provide: MultiSelectDropDownSettingService, useValue: multiSelectServiceSpy },
        { provide: KeyValueMappingServiceService, useValue: keyValueServiceSpy },
        commonsProviders(null)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DeviceFilterComponent);
    component = fixture.componentInstance;
    deviceFilterService = TestBed.inject(DeviceOperationService) as jasmine.SpyObj<DeviceOperationService>;
    deviceService = TestBed.inject(DeviceService) as jasmine.SpyObj<DeviceService>;
    salesOrderApiCallService = TestBed.inject(SalesOrderApiCallService) as jasmine.SpyObj<SalesOrderApiCallService>;
    countryCacheService = TestBed.inject(CountryCacheService) as jasmine.SpyObj<CountryCacheService>;
    commonsService = TestBed.inject(CommonsService) as jasmine.SpyObj<CommonsService>;
    commonOperationsService = TestBed.inject(CommonOperationsService) as jasmine.SpyObj<CommonOperationsService>;
    exceptionService = TestBed.inject(ExceptionHandlingService) as jasmine.SpyObj<ExceptionHandlingService>;
    multiSelectService = TestBed.inject(MultiSelectDropDownSettingService) as jasmine.SpyObj<MultiSelectDropDownSettingService>;
    keyValueService = TestBed.inject(KeyValueMappingServiceService) as jasmine.SpyObj<KeyValueMappingServiceService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize form controls', () => {
      expect(component.filterForm).toBeDefined();
      expect(component.filterForm.get('packageVersions')).toBeDefined();
      expect(component.filterForm.get('connectionState')).toBeDefined();
      expect(component.filterForm.get('deviceLockState')).toBeDefined();
      expect(component.filterForm.get('deviceEditState')).toBeDefined();
      expect(component.filterForm.get('countries')).toBeDefined();
      expect(component.filterForm.get('drpDeviceType')).toBeDefined();
      expect(component.filterForm.get('salesOrderNumber')).toBeDefined();
      expect(component.filterForm.get('productStatus')).toBeDefined();
      expect(component.filterForm.get('deviceId')).toBeDefined();
      expect(component.filterForm.get('deviceSerialNo')).toBeDefined();
      expect(component.filterForm.get('customerName')).toBeDefined();
    });

    it('should initialize dropdown settings', () => {
      // Setup
      const mockSettings = { singleSelection: false, text: 'Select' };
      multiSelectService.getLockStateDropdownSetting.and.returnValue(mockSettings as any);
      multiSelectService.getEditStateDropdownSetting.and.returnValue(mockSettings as any);
      multiSelectService.getSystemSoftwearVersionDropdownSetting.and.returnValue(mockSettings as any);
      multiSelectService.getDeviceConnectionStateDropdownSetting.and.returnValue(mockSettings as any);
      multiSelectService.getDeviceTypeDropdownSetting.and.returnValue(mockSettings as any);
      multiSelectService.getSalesOrderNumberDrpSetting.and.returnValue(mockSettings as any);
      multiSelectService.getCountryDrpSetting.and.returnValue(mockSettings as any);
      multiSelectService.getProductStatusDrpSetting.and.returnValue(mockSettings as any);

      // Call
      component.ngOnInit();

      // Verify
      expect(multiSelectService.getLockStateDropdownSetting).toHaveBeenCalled();
      expect(multiSelectService.getEditStateDropdownSetting).toHaveBeenCalled();
      expect(multiSelectService.getSystemSoftwearVersionDropdownSetting).toHaveBeenCalled();
      expect(multiSelectService.getDeviceConnectionStateDropdownSetting).toHaveBeenCalled();
      expect(multiSelectService.getDeviceTypeDropdownSetting).toHaveBeenCalled();
      expect(multiSelectService.getSalesOrderNumberDrpSetting).toHaveBeenCalled();
      expect(multiSelectService.getCountryDrpSetting).toHaveBeenCalled();
      expect(multiSelectService.getProductStatusDrpSetting).toHaveBeenCalled();
    });

    it('should initialize dropdown data', () => {
      // Setup
      const mockEnumList = [{ key: 'ENABLED', value: 'Enable' }];
      const mockBooleanList = [{ key: 'Yes', value: true }];
      keyValueService.enumOptionToList.and.returnValue(mockEnumList);
      keyValueService.lockedUnlockOptionList.and.returnValue(mockBooleanList);
      keyValueService.editEnableDisableOptionList.and.returnValue(mockBooleanList);

      // Call
      component.ngOnInit();

      // Verify
      expect(keyValueService.enumOptionToList).toHaveBeenCalledTimes(2);
      expect(keyValueService.lockedUnlockOptionList).toHaveBeenCalled();
      expect(keyValueService.editEnableDisableOptionList).toHaveBeenCalled();
      expect(component.deviceTypes).toContain(ClientDevice);
      expect(component.deviceTypes).toContain(TestDevice);
      expect(component.deviceTypes).toContain(DemoDevice);
    });

    it('should subscribe to device list refresh subject', () => {
      // Setup
      const mockParam = new ListingPageReloadSubjectParameter(true, true, true, false);
      spyOn(component, 'clearFilter');

      // Call
      component.onInitSubject();
      refreshSubject.next(mockParam);

      // Verify
      expect(component.clearFilter).toHaveBeenCalledWith(mockParam);
    });

    it('should handle page change in device list refresh subject', () => {
      // Setup
      const mockParam = new ListingPageReloadSubjectParameter(true, true, false, false);
      spyOn(component as any, 'deviceListPageRefresh');

      // Call
      component.onInitSubject();
      refreshSubject.next(mockParam);

      // Verify
      expect((component as any).deviceListPageRefresh).toHaveBeenCalledWith(mockParam);
    });
  });

  describe('Data Loading', () => {
    it('should use cached data when cache is not empty', async () => {
      // Setup cached data
      const cachedPackageVersions = ['v1.0.0', 'v2.0.0'];
      const cachedSalesOrders = ['SO001', 'SO002'];
      const cachedCountries: CountryListResponse[] = [{ id: 1, country: 'USA', languages: ['English'] }];

      deviceFilterService.getPackageVersionList.and.returnValue(cachedPackageVersions);
      deviceFilterService.getSalesOrderNumberList.and.returnValue(cachedSalesOrders);
      deviceFilterService.getCountryList.and.returnValue(cachedCountries);

      await component.getInitCall();

      // Verify cached data is used
      expect(component.packageVersionsList).toEqual(cachedPackageVersions);
      expect(component.salesOrderNumberList).toEqual(cachedSalesOrders);
      expect(component.countryList).toEqual(cachedCountries);

      // Verify API calls are not made
      expect(deviceService.getpackageVersion).not.toHaveBeenCalled();
      expect(salesOrderApiCallService.getSalesOrderNumberList).not.toHaveBeenCalled();
      expect(countryCacheService.getCountryListFromCache).not.toHaveBeenCalled();
    });

    it('should make API calls when cache is empty', async () => {
      // Setup empty cache
      deviceFilterService.getPackageVersionList.and.returnValue([]);
      deviceFilterService.getSalesOrderNumberList.and.returnValue([]);
      deviceFilterService.getCountryList.and.returnValue([]);

      // Setup API responses
      const apiPackageVersions = ['v1.0.0'];
      const apiSalesOrders = ['SO001'];
      const apiCountries: CountryListResponse[] = [{ id: 1, country: 'USA', languages: ['English'] }];

      deviceService.getpackageVersion.and.returnValue(of({ body: apiPackageVersions } as any));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(apiSalesOrders));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve(apiCountries));
      commonsService.checkForNull.and.returnValue(apiPackageVersions);

      await component.getInitCall();

      // Verify API calls are made
      expect(deviceService.getpackageVersion).toHaveBeenCalled();
      expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
      expect(countryCacheService.getCountryListFromCache).toHaveBeenCalledWith(true);
    });

    it('should handle API errors gracefully', async () => {
      // Setup empty cache
      deviceFilterService.getPackageVersionList.and.returnValue([]);
      deviceFilterService.getSalesOrderNumberList.and.returnValue([]);
      deviceFilterService.getCountryList.and.returnValue([]);

      // Setup API error responses
      deviceService.getpackageVersion.and.returnValue(throwError(() => new Error('API error')));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve([]));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([]));

      await component.getInitCall();

      // Verify error handler was called
      expect(exceptionService.customErrorMessage).toHaveBeenCalled();
    });
  });

  describe('Filter Value Setting', () => {
    it('should set filter values from deviceSearchRequestBody', () => {
      // Setup
      const mockSearchRequest = new DeviceSearchRequest(
        ['v1.0.0'],
        'CONNECTED',
        ClientDevice,
        'HW123',
        'SN123',
        'Customer1',
        [1, 2],
        true,
        false,
        ['SO001'],
        [ProductStatusEnum.ENABLED]
      );
      component.deviceSearchRequestBody = mockSearchRequest;
      component.countryList = [
        { id: 1, country: 'USA', languages: ['English'] },
        { id: 2, country: 'Canada', languages: ['English', 'French'] }
      ];
      component.lockUnlockStateList = [{ key: 'Yes', value: true }];
      component.editStateList = [{ key: 'No', value: false }];

      // Setup commons service mocks
      commonsService.getEnumMappingSelectedValue.and.returnValue([{ key: 'CONNECTED', value: 'Connected' }]);

      // Call
      (component as any).setFilterValue();

      // Verify form values are set
      expect(component.filterForm.get('packageVersions')?.value).toEqual(['v1.0.0']);
      expect(component.filterForm.get('deviceId')?.value).toBe('HW123');
      expect(component.filterForm.get('deviceSerialNo')?.value).toBe('SN123');
      expect(component.filterForm.get('customerName')?.value).toBe('Customer1');
      expect(component.filterForm.get('salesOrderNumber')?.value).toEqual(['SO001']);
    });

    it('should handle null deviceSearchRequestBody', () => {
      // Setup
      component.deviceSearchRequestBody = null;

      // Call
      (component as any).setFilterValue();

      // Verify no errors and form remains empty
      expect(component.filterForm.get('deviceId')?.value).toBe('');
      expect(component.filterForm.get('deviceSerialNo')?.value).toBe('');
      expect(component.filterForm.get('customerName')?.value).toBe('');
    });

    it('should trigger page refresh when listPageRefreshForbackToDetailPage is true', () => {
      // Setup
      component.listPageRefreshForbackToDetailPage = true;
      spyOn(component as any, 'deviceListPageRefresh');

      // Call
      (component as any).setFilterValue();

      // Verify
      expect((component as any).deviceListPageRefresh).toHaveBeenCalled();
    });
  });

  describe('Country Selection', () => {
    it('should handle country selection with special item', () => {
      // Setup
      const specialItem = { id: -1, country: 'Other' };
      const mockCountryList = [
        { id: 1, country: 'USA', languages: ['English'] },
        { id: 2, country: 'Canada', languages: ['English', 'French'] }
      ];
      component.countryList = mockCountryList;
      multiSelectService.setOtherOptionDisabled.and.returnValue(mockCountryList);

      // Call
      component.onCountrySelect(specialItem);

      // Verify
      expect(component.filterForm.get('countries')?.value).toEqual([specialItem]);
      expect(multiSelectService.setOtherOptionDisabled).toHaveBeenCalledWith(mockCountryList);
    });

    it('should handle country selection with regular item', () => {
      // Setup
      const regularItem = { id: 1, country: 'USA' };

      // Call
      component.onCountrySelect(regularItem);

      // Verify special handling is not triggered
      expect(multiSelectService.setOtherOptionDisabled).not.toHaveBeenCalled();
    });

    it('should handle country deselection', () => {
      // Setup
      const mockCountryList = [
        { id: 1, country: 'USA', languages: ['English'] },
        { id: 2, country: 'Canada', languages: ['English', 'French'] }
      ];
      component.countryList = mockCountryList;
      multiSelectService.setAllOptionEnable.and.returnValue(mockCountryList);

      // Call
      component.onCountryDeSelect();

      // Verify
      expect(multiSelectService.setAllOptionEnable).toHaveBeenCalledWith(mockCountryList);
    });
  });

  describe('Search and Filter Operations', () => {
    it('should show toast message when filter is empty', () => {
      // Setup
      commonsService.checkValueIsNullOrEmpty.and.returnValue(true);

      // Call
      component.searchData();

      // Verify
      expect(commonOperationsService.showEmptyFilterTosteMessge).toHaveBeenCalled();
      expect(deviceFilterService.callDeviceListFilterRequestParameterSubject).not.toHaveBeenCalled();
    });

    it('should refresh device list when filter has values', () => {
      // Setup
      commonsService.checkValueIsNullOrEmpty.and.returnValues(false, true, true, true, true, true, true, true, true, true, true);
      commonsService.checkNullFieldValue.and.returnValue('');
      spyOn(component as any, 'deviceListPageRefresh');

      // Call
      component.searchData();

      // Verify
      expect((component as any).deviceListPageRefresh).toHaveBeenCalled();
    });

    it('should clear all filters', () => {
      // Setup
      spyOn(component, 'onCountryDeSelect');
      spyOn(component.filterForm, 'reset');
      spyOn(component.filterForm, 'patchValue');
      spyOn(component as any, 'deviceListPageRefresh');

      // Call
      component.clearFilter();

      // Verify
      expect(component.onCountryDeSelect).toHaveBeenCalled();
      expect(component.filterForm.reset).toHaveBeenCalled();
      expect(component.filterForm.patchValue).toHaveBeenCalled();
      expect((component as any).deviceListPageRefresh).toHaveBeenCalled();
    });

    it('should create device search request from form values', () => {
      // Setup
      component.filterForm.patchValue({
        packageVersions: ['v1.0.0'],
        connectionState: [{ key: 'CONNECTED', value: 'Connected' }],
        deviceLockState: [{ key: 'Yes', value: true }],
        deviceEditState: [{ key: 'No', value: false }],
        countries: [{ id: 1, country: 'USA', languages: ['English'] }],
        drpDeviceType: [ClientDevice],
        salesOrderNumber: ['SO001'],
        productStatus: [{ key: 'ENABLED', value: 'Enable' }],
        deviceId: 'HW123',
        deviceSerialNo: 'SN123',
        customerName: 'Customer1'
      });

      commonsService.checkNullFieldValue.and.callFake(val => val);
      commonsService.getIdsFromArray.and.returnValue([1]);
      commonsService.getSelectedValueFromEnum.and.returnValue(['ENABLED']);
      commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValues(true, false);
      commonsService.getDeviceTypeStringToEnum.and.returnValue(ClientDevice);

      // Call
      const result = (component as any).getDeviceSearchRequest();

      // Verify
      expect(result).toBeDefined();
      expect(result.packageVersions).toEqual(['v1.0.0']);
      expect(result.deviceId).toBe('HW123');
      expect(result.deviceSerialNo).toBe('SN123');
      expect(result.customerName).toBe('Customer1');
      expect(result.countryIds).toEqual([1]);
      expect(result.deviceLockStatus).toBe(true);
      expect(result.isEditable).toBe(false);
    });

    it('should send filter data to listing component', () => {
      // Setup
      const mockParam = new ListingPageReloadSubjectParameter(true, true, false, false);
      spyOn(component as any, 'getDeviceSearchRequest').and.returnValue(new DeviceSearchRequest(
        [], null, null, '', '', '', null, null, null, [], []
      ));

      // Call
      (component as any).deviceListPageRefresh(mockParam);

      // Verify
      expect(deviceFilterService.callDeviceListFilterRequestParameterSubject).toHaveBeenCalled();
      const callArg = deviceFilterService.callDeviceListFilterRequestParameterSubject.calls.mostRecent().args[0];
      expect(callArg.listingPageReloadSubjectParameter).toBe(mockParam);
      expect(callArg.deviceSearchRequest).toBeDefined();
    });

    it('should reset form if invalid before refreshing', () => {
      // Setup
      const mockParam = new ListingPageReloadSubjectParameter(true, true, false, false);
      spyOn(component.filterForm, 'reset');
      spyOn(component as any, 'getDeviceSearchRequest').and.returnValue({});
      component.filterForm.setErrors({ invalid: true });

      // Call
      (component as any).deviceListPageRefresh(mockParam);

      // Verify
      expect(component.filterForm.reset).toHaveBeenCalled();
    });
  });

  describe('Component Lifecycle', () => {
    it('should unsubscribe on destroy', () => {
      // Setup
      component.subscriptionForRefreshList = jasmine.createSpyObj('Subscription', ['unsubscribe']);

      // Call
      component.ngOnDestroy();

      // Verify
      expect(component.subscriptionForRefreshList.unsubscribe).toHaveBeenCalled();
    });

    it('should not throw error if subscription is null on destroy', () => {
      // Setup
      component.subscriptionForRefreshList = null;

      // Call & Verify (no error)
      expect(() => component.ngOnDestroy()).not.toThrow();
    });

    it('should initialize with API calls when isFilterComponentInitWithApicall is true', () => {
      // Setup
      component.isFilterComponentInitWithApicall = true;
      spyOn(component, 'clearFilter');

      // Call
      component.ngOnInit();

      // Verify
      expect(component.clearFilter).toHaveBeenCalled();
    });

    it('should not clear filter when isFilterComponentInitWithApicall is false', () => {
      // Setup
      component.isFilterComponentInitWithApicall = false;
      spyOn(component, 'clearFilter');

      // Call
      component.ngOnInit();

      // Verify
      expect(component.clearFilter).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle errors when getting package versions', async () => {
      // Setup
      deviceFilterService.getPackageVersionList.and.returnValue([]);
      deviceService.getpackageVersion.and.returnValue(throwError(() => new Error('API error')));

      // Call
      await (component as any).getpackageVersion();

      // Verify
      expect(exceptionService.customErrorMessage).toHaveBeenCalled();
    });

    it('should validate deviceId field', () => {
      // Setup
      const deviceIdControl = component.filterForm.get('deviceId');

      // Test valid input
      deviceIdControl.setValue('valid-id');
      expect(deviceIdControl.valid).toBeTruthy();

      // Test too long input
      const longString = 'a'.repeat(51); // Assuming Small_TextBoxMaxLength is 50
      deviceIdControl.setValue(longString);
      expect(deviceIdControl.hasError('maxlength')).toBeTruthy();
    });

    it('should validate deviceSerialNo field', () => {
      // Setup
      const serialNoControl = component.filterForm.get('deviceSerialNo');

      // Test valid input
      serialNoControl.setValue('SN12345');
      expect(serialNoControl.valid).toBeTruthy();

      // Test invalid pattern (single quote not allowed)
      serialNoControl.setValue("Invalid'SerialNo");
      expect(serialNoControl.hasError('pattern')).toBeTruthy();
    });

    it('should validate customerName field', () => {
      // Setup
      const customerNameControl = component.filterForm.get('customerName');

      // Test valid input
      customerNameControl.setValue('Valid Customer Name');
      expect(customerNameControl.valid).toBeTruthy();

      // Test too long input
      const longString = 'a'.repeat(256); // Assuming TextBoxMaxLength is 255
      customerNameControl.setValue(longString);
      expect(customerNameControl.hasError('maxlength')).toBeTruthy();

      // Test invalid pattern (single quote not allowed)
      customerNameControl.setValue("Invalid'Customer'Name");
      expect(customerNameControl.hasError('pattern')).toBeTruthy();
    });
  });

  describe('Edge Cases and Additional Coverage', () => {
    it('should handle null subscription in ngOnDestroy', () => {
      // Setup
      component.subscriptionForRefreshList = undefined;

      // Call & Verify (no error)
      expect(() => component.ngOnDestroy()).not.toThrow();
    });

    it('should handle empty form values in searchData', () => {
      // Setup - all form values are empty
      component.filterForm.patchValue({
        packageVersions: [],
        connectionState: null,
        deviceLockState: null,
        deviceEditState: null,
        countries: null,
        drpDeviceType: null,
        salesOrderNumber: [],
        productStatus: [],
        deviceId: '',
        deviceSerialNo: '',
        customerName: ''
      });

      commonsService.checkNullFieldValue.and.returnValue('');
      commonsService.checkValueIsNullOrEmpty.and.returnValue(true);

      // Call
      component.searchData();

      // Verify
      expect(commonOperationsService.showEmptyFilterTosteMessge).toHaveBeenCalled();
    });

    it('should handle form validation in searchData', () => {
      // Setup - invalid form
      component.filterForm.setErrors({ invalid: true });
      commonsService.checkNullFieldValue.and.returnValue('');
      commonsService.checkValueIsNullOrEmpty.and.returnValue(true);

      // Call
      component.searchData();

      // Verify
      expect(commonOperationsService.showEmptyFilterTosteMessge).toHaveBeenCalled();
    });

    it('should handle null values in setFilterValue', () => {
      // Setup
      const mockSearchRequest = new DeviceSearchRequest(
        null, null, null, null, null, null, null, null, null, null, null
      );
      component.deviceSearchRequestBody = mockSearchRequest;
      component.countryList = [];
      component.lockUnlockStateList = [];
      component.editStateList = [];

      commonsService.getEnumMappingSelectedValue.and.returnValue([]);

      // Call
      (component as any).setFilterValue();

      // Verify form values are set to empty/null
      expect(component.filterForm.get('packageVersions')?.value).toEqual([]);
      expect(component.filterForm.get('deviceId')?.value).toBe('');
      expect(component.filterForm.get('deviceSerialNo')?.value).toBe('');
      expect(component.filterForm.get('customerName')?.value).toBe('');
    });

    it('should handle single connection state in getDeviceSearchRequest', () => {
      // Setup
      component.filterForm.patchValue({
        connectionState: [{ key: 'CONNECTED', value: 'Connected' }],
        countries: null,
        packageVersions: null,
        deviceLockState: null,
        deviceEditState: null,
        drpDeviceType: null,
        salesOrderNumber: null,
        productStatus: null,
        deviceId: null,
        deviceSerialNo: null,
        customerName: null
      });

      commonsService.checkNullFieldValue.and.callFake(val => val);
      commonsService.getIdsFromArray.and.returnValue(null);
      commonsService.getSelectedValueFromEnum.and.returnValue(['CONNECTED']);
      commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValues(null, null);
      commonsService.getDeviceTypeStringToEnum.and.returnValue(null);

      // Call
      const result = (component as any).getDeviceSearchRequest();

      // Verify single connection state is used
      expect(result.status).toBe('CONNECTED');
    });

    it('should handle multiple connection states in getDeviceSearchRequest', () => {
      // Setup
      component.filterForm.patchValue({
        connectionState: [
          { key: 'CONNECTED', value: 'Connected' },
          { key: 'DISCONNECTED', value: 'Disconnected' }
        ]
      });

      commonsService.getSelectedValueFromEnum.and.returnValue(['CONNECTED', 'DISCONNECTED']);

      // Call
      const result = (component as any).getDeviceSearchRequest();

      // Verify multiple connection states result in null
      expect(result.status).toBe(null);
    });

    it('should handle refresh subject with no reload data', () => {
      // Setup
      const mockParam = new ListingPageReloadSubjectParameter(false, true, false, false);
      spyOn(component, 'clearFilter');
      spyOn(component as any, 'deviceListPageRefresh');

      // Call
      component.onInitSubject();
      refreshSubject.next(mockParam);

      // Verify no actions are taken when isReloadData is false
      expect(component.clearFilter).not.toHaveBeenCalled();
      expect((component as any).deviceListPageRefresh).not.toHaveBeenCalled();
    });

    it('should handle clearFilter with custom parameter', () => {
      // Setup
      const customParam = new ListingPageReloadSubjectParameter(true, false, true, true);
      spyOn(component as any, 'clearAllFilter');
      spyOn(component as any, 'deviceListPageRefresh');

      // Call
      component.clearFilter(customParam);

      // Verify
      expect((component as any).clearAllFilter).toHaveBeenCalled();
      expect((component as any).deviceListPageRefresh).toHaveBeenCalledWith(customParam);
    });
  });
});
