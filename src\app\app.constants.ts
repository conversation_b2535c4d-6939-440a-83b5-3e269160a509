// These constants are injected via webpack environment variables.
// You can add more variables in webpack.common.js or in profile specific webpack.<dev|prod>.js files.
// If you change the values in the webpack config files, you need to re run webpack to update the application
export const ITEMS_PER_PAGE = 10;
export const RdmAction = "RdmAction_Permission"
export const RdmEmailToken = "RdmEmailToken"
export const RdmUserId = "RdmUserId"
export const RdmAuthenticationToken = "RdmAuthenticationToken"
export const RdmUserAssignedCountries = "RdmUserAssignedCountries"
export const RdmUserRoles = "RdmUserRoles";

export const ClientDevice = "Client Device";
export const TestDevice = "Test Device";
export const DemoDevice = "Demo Device";
export const NotAssociated = "Not Associated";
export const Locked = "Locked";
export const Unlocked = "Unlocked";
export const Active = "Active";
export const Inactive = "Inactive";
export const Edit_Enable = "Allowed";
export const Edit_Disable = "Not Allowed";
export const notAssociated = "notAssociated";

// common popup constants
export const Confirmation = 'Confirmation';
export const CancelBtn = 'Cancel'

//add Probe form constants
export const EnterSalesOrderNumber = "Sales Order Number is required";
export const EnterQRCode = "Please enter serial number";
export const EnterValidSerialNumber = "Serial Number can only contain letters, digits, hyphens (-), and underscores ( _ ), but it must start with a letter or number and cannot begin with a hyphen (-), underscore ( _ )";
export const SerialNumberExists = "Probe serial number already exists";
export const EnterCustomerName = "Customer Name is required";
export const EnterEmail = "Please enter E-mail ID";
export const EnterValidEmail = "Please enter valid E-mail ID";
export const ProbeTypeMessage = "Please select probe type";
export const CountryValidMessage = "Country selection is mandatory.";


//Error Message
export const SpecialCharacterErrorMessage = "Single quotes are not allowed.";
export const SPECIAL_CHARACTER_PATTERN = "^[^']*$";
export const SERIAL_NUMBER_PATTERN = "^[a-zA-Z0-9_-]*[a-zA-Z0-9]$";
export const EMAIL_VALIDATION_PATTERN = '^(?=.{1,64}@)[A-Za-z0-9_-]+(\\.[A-Za-z0-9_-]+)*[^-][A-Za-z0-9-]+(\\.[A-Za-z0-9-]+)*(\\.[A-Za-z]{2,})$';

//Search Limit
export const Min_Search_Character_Limit = 3;
export const Min_Search_Character_Limit_Message = "Enter Minimum 3 character";
//TextBox Max Length
export const Small_TextBoxMaxLength = 50;
export const Small_TextBoxMaxCharactersAllowedMessage = "Maximum 50 characters allowed";
export const Medium_TextBoxMaxLength = 150;
export const Medium_TextBoxMaxCharactersAllowedMessage = "Maximum 150 characters allowed";
export const TextBoxMaxLength = 250;
export const TextBoxMaxCharactersAllowedMessage = "Maximum 250 characters allowed";
export const TextAreaMaxLength = 2000;
export const TextAreaMaxCharactersAllowedMessage = "Maximum 2000 characters allowed";

//Password policy
export const OLD_PASSWORD_MIN_LENGTH = 3;
export const OLD_PASSWORD_MIN_MESSAGE = "Enter Password Minimum 3 character";
export const PASSWORD_MAX_LENGTH = 16;
export const PASSWORD_MAX_LENGTH_MESSAGE = "Maximum 16 characters allowed";
export const PASSWORD_PATTERN = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[`~!@#$%^&+=()\\-\\_\\*])(?=\\S+$).{12,16}$";
export const PASSWORD_PATTERN_MESSAGE = "Password must be minimum 12 characters and maximum 16 characters long. Include at least 1 uppercase letter, 1 lowercase, 1 digit & 1 special character ( `~!@#$%^&+=()-_* ) with no space.";

// delete Probe Constants
export const DeleteProbeConfirmationHeader = "Are you sure?";
export const DeleteProbeConfirmationMessage = "Are you sure you want to delete the probe(s)?";
export const DeleteProbeConfirmationConfirmBtn = "Confirm";

// update Feature Association constants
export const FeatureAssociationConfirmationHeader = "Are you sure?";
export const FeatureAssociationConfirmationMessageForProbeDetail = "Clicking confirm will configure license for the selected probe."
export const FeatureAssociationConfirmationMessageForProbeList = "Please note, the features that you selected will be applied and previously selected features will be erased. Are you sure you want to proceed?"
export const FeatureAssociationConfirmationConfirmBtn = "Confirm";

export const ValidationMessageForTrialFeature = "* End date should not be Unlimited while feature is in Trial Mode";
export const FeatureLicenseEmptySasUrl = "License is not configured for the probe";

// Feature History detail constants
export const FeatureHistoryDetailHeader = "License History Details";


// nesuite id and customer email Association constants
export const SalesOrderAssociationHeader = "Customer / Sales Order Association";
export const Submit = "Submit";
export const Cancel = "Cancel";
export const Confirm = "Confirm";
export const Close = "Close";

// add association popup message constants
export const AssociateBtn = 'Associate';
export const AssociationMessage = 'Are you sure you want to update the association? Doing same will remove the release association with test device if any.';

// remove association popup message constants
export const DeassociateBtn = 'Deassociate';
export const DeassociationMessage = 'Are you sure you want to remove the association? Doing same will remove the release association with test device if any.';

//Date Format
export const DateDisplayFormat = "MMM d, y";
export const DateTimeDisplayFormat = "MMM d, y, h:mm:ss a";

//Upload Update video json
export const UploadVideoSuccessMessage = "Video uploaded successfully";
export const UpdateVideoSuccessMessage = "Video updated successfully";
export const CreateJsonSuccessMessage = "Json created successfully";
export const UpdateJsonSuccessMessage = "Json updated successfully";

//Download Video File error
export const DownloadVideoFileError = "Error while downloading video";

// add Probe
export const AddProbeHeader = "Add Probe";
export const ProbeSuccessMessage = "Probes Created Successfully";
export const ProbeConflictErrorMessage = "Probe already exist";
export const ProbeConflictErrorMessageTimeOut = 900000

// configure Features
export const ConfigureFeaturesHeader = "Configure License";

//Resource list
export const AddProbeResource = "addProbeResource";
export const ProbListResource = "probListResource";
export const AddMultipleProbeResource = "addMultipleProbeResource";
export const ProbDetailResource = "probDetailResource";
export const DeviceListResource = "deviceListResource";
export const DeviceDetailResource = "deviceDetailResource";
export const ListRoleResource = "listRoleResource";
export const DetailRoleResource = "detailRoleResource";
export const ListSalesOrderResource = "listSalesOrderResource";
export const DetailSalesOrderResource = "detailSalesOrderResource";
export const ListKitManagementResource = "listKitManagementResource";
export const OTSKitManagementListResource = "listOTSKitManagementResource";
export const DetailKitManagementResource = "detailKitManagementResource";
export const DetailOTSKitManagementResource = "detailOTSKitManagementResource";
export const ListCountryResource = "listCountryResource";
export const ListAuditResource = "listAuditResource";
export const SoftwareBuildListResource = "softwareBuildListResource";
export const ListProbeFearureGroupResource = "listProbeFetureGroup";
export const DeatilsProbeFearureGroupResource = "detailsProbeFetureGroup";
export const TransferOrderResource = "transferOrderResource";
export const DeviceLogListResource = "deviceLogListResource";
export const DeviceActivityListResource = "deviceActivityListResource";


//Prob download popup
export const probDownloadOkButtonText = "Ok";
export const probDownloadCancelButtonText = "Cancel";
export const probDownloadTitleButtonText = "Probe License Download";
export const probDownloadBodyButtonText = "License file will be downloaded only for those probes where license is configured.";

//Software build messages
export const SoftwareBuild_Map_ClientDevice = "Are you sure you want to map Software Build(s) with Client devices?";
export const SoftwareBuild_Map_DemoDevice = "Are you sure you want to map Software Build(s) with Demo devices?";
export const SoftwareBuild_Map_ClientAndDemoDevice = "Are you sure you want to map Software Build(s) with Client and Demo devices?";

//Model Size
export const Large_Size_Model = 'lg';
export const Medium_Size_Model = 'md';
export const Small_Size_Model = "sm";

//Update Probe Type 
export const Update_Probe_title = "Update Probe Type(s)";
export const Update_Probe_OkButton = "Update";
export const Update_Probe_CancelButton = "Cancel";

//DeVice Type Message
export const DEVICE_CONVERT_TO_TEST = "Device(s) successful Convert To Test device";
export const DEVICE_CONVERT_TO_DEMO = "Device(s) successful Convert To Demo device";
export const DEVICE_CONVERT_TO_CLIENT = "Device(s) successful Convert To Client device";
export const DEVICE_ALREADY_TEST = "This device is already Test device";
export const DEVICE_ALREADY_DEMO = "This device is already Demo device";
export const DEVICE_ALREADY_CLIENT = "This device is already Client device";


//Probe Listing 
export const COMMON_SELECT_FILTER = "Please Select Filter To Search";
export const HISTORICAL_DATA_FILTER_APPLY_MESSAGE = "Please apply at least one filter in the Historical Data section before the search."
export const Probe_Select_Message = "Please select Probe";
export const Probe_Single_Select_Message = "Please select Single Probe";
export const Device_Select_Message = "Please select Device";
//Delete Role
export const ROLE_DELETE = "This Role is Deleted";
export const USER_DELETE = "This User is Deleted";
export const PROBE_DELETE = "This Probe is Deleted";
export const BRIDGE_KIT_MANAGEMANT_DELETE = "This Bridge Kit is Deleted";
export const OTS_KIT_MANAGEMANT_DELETE = "This Ots Kit is Deleted";
export const PROBE_CONFIG_GROUP_DELETE = "This Probe Config Group is Deleted";
export const DEVICE_DELETE = "Invalid Device Id";

//Pattern 
export const Find_Letter_Pattern = /[A-Z]/i;
export const VERSION_PATTERN = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
export const JSON_VERSION_PATTERN = /^(\d{1,3})\.(\d{1,3})$/;


export const SERACH_REG_EXP_FOR_COMMA_PATTERN = /,/g;
export const REPLACE_COMMA_STRING = " , ";

//AUDIT 
export const AUDIT_MODIFIED_START_DATE_FILTER = "Please Provide Modified Start Date";
export const AUDIT_MODIFIED_END_DATE_FILTER = "Please Provide Modified End Date";
export const AUDIT_MODULE_UNIQUE_ID_FILTER = "Please Provide Module";

//
export const PERMISSION_NAME_HIDE = [];

export const INTERNAL_SERVER_ERROR = "Internal Server Error";
export const API_TIME_OUT = "The server is taking too long to respond. Please try again later.";
//Audit System PK
export const AUDIT_SYSTEM_PK_ID = -1;

//Device Edit Enable/Disable Message 
export const DEVICE_ALREADY_EDIT_ENABLE = "Device is already marked as editable.";
export const DEVICE_ALREADY_EDIT_DISABLE = "Device is already marked as read-only.";
export const DEVICE_EDIT_DISABLE_NO_ACTION_ALLOWED_MESSAGE = "Selected device is marked as read-only. Updates are not allowed.";
export const DEVICE_HAS_NO_SERIAL_NUMBER = "Device must have a valid Serial Number.";
export const DEVICE_STATUS_NOT_ENABLE = "Device status must be Enable.";
export const SALES_ORDER_PARTIALLY_CONFIGURED = "Transfer not allowed. Sales order must not be in Partially Configured state."

//probe Disable/RMA Message
export const PROBE_SERIAL_NUMBER_NOT_FOUND = "Probe status is not allowed to update because the serial number does not exist with the probe.";
export const PROBE_SERIAL_NUMBER_NOT_EMPTY = "Probe must have a valid Serial Number.";
export const PROBE_STATUS_ENABLE = "Probe status must be Enable.";
//Probe Edit Enable/Disable Message
export const PROBE_ALREADY_EDIT_ENABLE = "Probe is already marked as editable.";
export const PROBE_ALREADY_EDIT_DISABLE = "Probe is already marked as read-only.";
export const PROBE_EDIT_DISABLE_NO_ACTION_ALLOWED_MESSAGE = "Selected probe is marked as read-only. Updates are not allowed.";

//VIOLATION_UNIQUE_KEY 
export const VIOLATION_UNIQUE_KEY_CONSTRAINT = "Violation of UNIQUE KEY constraint";
export const PROBE_ALREADY_EXIEST = "Probe(s) already exist";


//Module Country Validation message
export const MODULE_USER_WITH_NO_COUNTRY_MESSAGE = "Unable to modify selected item as no country has been assigned to you.";
export const MODULE_VALIDATE_WITH_USER_COUNTRY_INVALID_PROBE = "Unable to modify selected probe(s) as the associated country is not assigned to you.";
export const MODULE_VALIDATE_WITH_USER_COUNTRY_INVALID_DEVICE = "Unable to modify selected device(s) as the associated country is not assigned to you.";
export const MODULE_VALIDATE_WITH_USER_COUNTRY_INVALID_SOFTWARE_BUILD = "Unable to modify selected software build(s) as the associated country is not assigned to you.";
//Lock-UnLock Validation
export const DEVICE_ALREADY_LOCKED = "Device already locked";
export const DEVICE_ALREADY_UNLOCKED = "Device already unlocked";
export const PROBE_ALREADY_LOCKED = "Probe is already locked";
export const PROBE_ALREADY_UNLOCKED = "probe is already unlocked";

//SSO
export const SSO_FAILED_TITLE = "Alert";
export const SSO_FAILED_MESSAGE = "Access denied. Please contact the administrator to set up your account.";

//Probe-Config-Group
export const DeleteProbeConfigGroupConfirmationMessage = "Are you sure you want to delete this probe configuration group? This action cannot be undone."
export const PartNumberRequire = "PartNumber is required"

//Sales Order
export const SALES_ORDER_DELETED_OR_USER_DEFINED = "This sales order has either been deleted or is user-defined."
export const DELETE_SALES_ORDER_CONFIRMATION_MESSAGE = "Are you sure you want to delete this sales Order? This action cannot be undone."
export const SALES_ORDER_NOT_SELECTED = "Please Select a Sales Order."
export const OREDER_RECORD_TYPE_MANDATORY = "Order Record Type selection is mandatory."
export const MANUAL_ORDER_CREATED = "Manual Sales Order Created Successfully."

//Transfer Order
export const TRANSFER_ORDER = "Transfer Order"
export const TREANSFER_ORDER_EMPTY_LIST = "Non-Transfer Sales Orders are not available."
export const EMPTY_TRANSFER_ORDER_PRODUCT = "Transfer Sales Order or Transferred Sales Order not available."
export const EMPTY_TRANSFER_ORDER_VALIDATE_PRODUCT = "There are no transfer sales order products available for review and validation."

// Country Module
export const COUNTRY_ALREADY_EXISTING = "Country already exists"
export const DELETE_COUNTRY_CONFIRMATION_MESSAGE = "Are you sure you want to delete this country? This action cannot be undone."
export const COUNTRY_NOT_SELECTED = "Please select a country."

// Audit Module
export const REVERSE_TRANSFER_PRODUCT_CONFIRMATION_MESSAGE = "Are you sure you want to reverse the product? This action cannot be undone."

//API Status Code Error Messages
export const FORBUDDEN_ERROR_MESSAGE = "Invalid Input";