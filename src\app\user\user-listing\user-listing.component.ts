import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { DateTimeDisplayFormat, ITEMS_PER_PAGE, SPECIAL_CHARACTER_PATTERN, SpecialCharacterErrorMessage, TextBoxMaxCharactersAllowedMessage, TextBoxMaxLength } from '../../app.constants';
import { ConfirmDialogService } from '../../confirmationdialog/confirmation.service';
import { MultiSelectDropdownSettings } from '../../model/MultiSelectDropdownSettings.model';
import { UserListPageResponse } from '../../model/User/UserListPageResponse.model';
import { UserSearchResponse } from '../../model/User/UserSearchResponse';
import { ExceptionHandlingService } from '../../shared/ExceptionHandling.service';
import { MessageService } from '../../shared/Message.service';
import { CountryCacheService } from '../../shared/Service/CacheService/countrycache.service';
import { RoleApiCallService } from '../../shared/Service/RoleService/role-api-call.service';
import { AuthJwtService } from '../../shared/auth-jwt.service';
import { PermissionAction } from '../../shared/enum/Permission/permissionAction.enum';
import { collapseFilterTextEnum } from '../../shared/enum/collapseFilterButtonText.enum';
import { PermissionService } from '../../shared/permission.service';
import { UserApiCallService } from '../../shared/Service/user/user-api-call.service';
import { CommonsService } from '../../shared/util/commons.service';
import { MultiSelectDropDownSettingService } from '../../shared/util/multi-select-drop-down-setting.service';
import { UserSearchRequest } from '../../model/User/userSearchRequest';
import { CommonCheckboxService } from '../../shared/util/common-checkbox.service';
import { SuccessMessageResponse } from '../../model/common/SuccessMessageResponse.model';
import { CountryListResponse } from '../../model/Country/CountryListResponse.model';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-user-listing',
  templateUrl: './user-listing.component.html',
  styleUrls: ['./user-listing.component.css']
})
export class UserListingComponent implements OnInit {

  userDetail: UserSearchResponse[] = [];

  //user detail page
  totalItems: number;
  page: number;
  itemsPerPage: number;
  previousPage: number;

  //loading
  loading: boolean = false;

  //member view and add user Show or Hide
  //first user list show
  allUserShow: boolean = true;
  addUserShow: boolean = false;
  singleUserShow: boolean = false;
  //user id list for delete
  userIdListcollect = [];

  //user id pass to single user component
  userId: number;

  //Id Collect
  localmemberIDListArray = [];

  //message
  totalMemberDisplay = 0;
  totalMember = 0;

  userRoleList: string[] = [];
  countrySetting: MultiSelectDropdownSettings;
  countryList: CountryListResponse[] = [];

  //Text box max limit set
  textBoxMaxLength: number = TextBoxMaxLength;
  textBoxMaxCharactersAllowedMessage: string = TextBoxMaxCharactersAllowedMessage;
  specialCharacterErrorMessage: string = SpecialCharacterErrorMessage;

  filterForm = this.fb.group({
    login: new FormControl('', [Validators.maxLength(this.textBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    userRole: new FormControl([]),
    country: new FormControl([])
  });

  //drp value set
  drpselectsize = ITEMS_PER_PAGE;
  roleDropdownSettings: any = {};
  isFilterHidden: boolean = false;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;

  // show entry selection
  dataSizes: string[] = [];

  //Permission
  userReaderPermission: boolean = false;
  deleteUserPermission: boolean = false;
  addUserPermission: boolean = false;

  dateTimeDisplayFormat: string = DateTimeDisplayFormat;

  //unique CheckBox Name
  chkPreFix = "user";
  selectAllCheckboxId = "selectAlluser";
  checkboxListName = "userData[]";
  tableRowPreFix = "userRow"

  constructor(
    private fb: FormBuilder,
    private message: MessageService,
    private dialogservice: ConfirmDialogService,
    private userApiCallService: UserApiCallService,
    private exceptionService: ExceptionHandlingService,
    private toste: ToastrService,
    private permissionService: PermissionService,
    private commonsService: CommonsService,
    private roleApiCallService: RoleApiCallService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private authservice: AuthJwtService,
    private countryCacheService: CountryCacheService,
    private commonCheckboxService: CommonCheckboxService
  ) { }

  ngOnInit() {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.loading = true;
      this.dataSizes = this.commonsService.accessDataSizes();

      this.drpselectsize = ITEMS_PER_PAGE;
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.page = 0;
      this.getInitData();

      this.allUserShow = true;
      this.addUserShow = false;
      this.singleUserShow = false;
      this.roleDropdownSettings = this.multiSelectDropDownSettingService.getRoleDrpSetting(false);
      this.countrySetting = this.multiSelectDropDownSettingService.getCountryDrpSetting(false, false);
      this.setUserPermission();
    }
  }

  /**
   * set User Permission
   */
  private setUserPermission(): void {
    this.userReaderPermission = this.permissionService.getUserPermission(PermissionAction.GET_USER_ACTION);
    this.deleteUserPermission = this.permissionService.getUserPermission(PermissionAction.DELETE_USER_ACTION);
    this.addUserPermission = this.permissionService.getUserPermission(PermissionAction.ADD_USER_ACTION);
  }

  /**
   * Get Role and country List
   * <AUTHOR>
   */
  private async getInitData(): Promise<void> {
    this.loading = true;
    this.initApiCall();
    this.getAlluserdetail();
  }

  /**
  * Initialize API calls for fetching role and country list.
  *
  * <AUTHOR>
  */
  public initApiCall(): void {
    forkJoin({
      userRoleList: this.roleApiCallService.getRoleNameList(),
      countryList: this.countryCacheService.getCountryListFromCache()
    }).subscribe({
      next: ({ userRoleList, countryList }) => {
        this.userRoleList = userRoleList;
        this.countryList = countryList;
      },
      error: (error) => {
        this.exceptionService.customErrorMessage(error);
      }
    })
  }

  /**
  * Refresh button click
  * 
  * <AUTHOR>
  */
  public clickOnRefreshButton(): void {
    this.initApiCall();
    this.getAlluserdetail();
  }

  /**
   * Clear all the filter
   * 
   * <AUTHOR>
   */
  private clearAllFilter(): void {
    this.filterForm.get('login').setValue(null);
    this.filterForm.get('userRole').setValue(null);
    this.filterForm.get('country').setValue(null);
    this.page = 0;
    this.userIdListcollect = [];
  }

  /**
   * Clear all the filter and reload all data
   */
  public clearFilter(): void {
    this.clearAllFilter();
    this.getAlluserdetail();
  }

  private setSearchData(): UserSearchRequest {
    let filterFormValue = this.filterForm.value;
    this.filterForm.get('login').setValue(this.commonsService.checkNullFieldValue(this.filterForm.get('login').value));
    return new UserSearchRequest(
      this.filterForm.get('login').value,
      this.commonsService.checkNullFieldValue(filterFormValue.userRole),
      this.commonsService.getIdsFromArray(filterFormValue.country));
  }

  public searchLogFilter(): void {
    let filterFormValue = this.filterForm.value;
    if (this.commonsService.checkValueIsNullOrEmpty(filterFormValue.login) &&
      this.commonsService.checkValueIsNullOrEmpty(filterFormValue.userRole) &&
      this.commonsService.checkValueIsNullOrEmpty(filterFormValue.country)) {
      this.toste.info(this.message.empty_filter);
    } else {
      this.page = 0;
      this.userIdListcollect = [];
      this.getAlluserdetail();
    }
  }

  public getAlluserdetail(): void {
    this.loading = true;
    if (this.filterForm.invalid) {
      this.filterForm.reset();
    }
    let searchRequest = this.setSearchData();
    this.userApiCallService.getMemberList(searchRequest, {
      page: this.page - 1,
      size: this.itemsPerPage
    }
    ).subscribe(
      {
        next: (response: HttpResponse<UserListPageResponse>) => {
          this.totalItems = response.body.totalElements;
          this.page = response.body.number + 1;
          this.userDetail = response.body.content;
          this.memberIdforDelete(response.body.content);
          this.totalMemberDisplay = response.body.numberOfElements;
          this.totalMember = response.body.totalElements;
          this.loading = false;
        },
        error: (error: HttpErrorResponse) => {
          this.loading = false;
          this.exceptionService.customErrorMessage(error);
        }
      });
  }

  private memberIdforDelete(memberList: Array<UserSearchResponse>): void {
    this.localmemberIDListArray = [];
    for (let member of memberList) {
      this.localmemberIDListArray.push(member.id);
    }
    this.defaultSelectAll();
  }

  public loadPage(page: number): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.loading = true;
      this.commonCheckboxService.clearSelectAllCheckbox(this.selectAllCheckboxId);
      this.getAlluserdetail();
    }
  }

  public addNewMember(): void {
    this.userIdListcollect = [];
    this.addUserShow = true;
    this.singleUserShow = false;
    this.allUserShow = false;
  }

  //call the add member component (emit())
  public viewAllmember(): void {
    this.loading = true;
    this.page = 0;
    this.getAlluserdetail();
    this.addUserShow = false;
    this.singleUserShow = false;
    this.allUserShow = true;
  }

  private singleMember(): void {
    this.addUserShow = false;
    this.singleUserShow = true;
    this.allUserShow = false;
  }

  /**
   * single Checkbox Select
   * <AUTHOR>
   * @param userObj 
   * @param isChecked 
   */
  public selectCheckbox(userObj: UserSearchResponse, isChecked: boolean): void {
    if (isChecked) {
      this.userIdListcollect.push(userObj.id);
    } else {
      let index = this.userIdListcollect.findIndex(obj => obj == userObj.id);
      this.userIdListcollect.splice(index, 1);
    }
    this.defaultSelectAll();
  }

  public deleteMultipleuser(): void {
    if (this.userIdListcollect.length > 0) {
      this.dialogservice.confirm('Delete', 'Please confirm to delete.')
        .then((confirmed) => {
          if (confirmed) {
            this.delete();
          }
        });
    }
    else {
      this.toste.info("Please Select User(s)")
    }
  }

  private delete(): void {
    this.loading = true;
    this.userApiCallService.deleteMultipleMember(this.userIdListcollect).subscribe({
      next: (response: HttpResponse<SuccessMessageResponse>) => {
        this.userIdListcollect = [];
        this.getAlluserdetail();
        this.toste.success(response.body.message);
      }, error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  public openMemberDetail(id: number): void {
    this.userIdListcollect = [];
    this.singleMember();
    this.userId = id;
  }

  public selectAllItem(isChecked: boolean): void {
    this.userIdListcollect = this.commonCheckboxService.selectAllItem(isChecked, this.localmemberIDListArray, this.userIdListcollect, this.checkboxListName);
  }

  public changeDataSize(datasize: any): void {
    this.loading = true;
    this.userIdListcollect = [];
    this.itemsPerPage = datasize.target.value;
    this.getAlluserdetail();
  }

  private defaultSelectAll(): void {
    this.commonCheckboxService.defaultSelectAll(this.localmemberIDListArray, this.userIdListcollect, this.selectAllCheckboxId);
  }

  /**
  * Toggle Filter
  * 
  */
  public toggleFilter(): void {
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

}
