import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Member } from '../../model/memberAdd.model';
import { MultiSelectDropdownSettings } from '../../model/MultiSelectDropdownSettings.model';
import { ExceptionHandlingService } from '../../shared/ExceptionHandling.service';
import { UserApiCallService } from '../../shared/Service/user/user-api-call.service';
import { RoleApiCallService } from '../../shared/Service/RoleService/role-api-call.service';
import { MultiSelectDropDownSettingService } from '../../shared/util/multi-select-drop-down-setting.service';
import { EMAIL_VALIDATION_PATTERN, TextBoxMaxCharactersAllowedMessage, TextBoxMaxLength } from '../../app.constants';
import { CountryListResponse } from '../../model/Country/CountryListResponse.model';
import { UserResponse } from '../../model/User/UserResponse.model';
import { CommonsService } from '../../shared/util/commons.service';
import { CountryCacheService } from '../../shared/Service/CacheService/countrycache.service';

@Component({
  selector: 'app-add-user',
  templateUrl: './add-user.component.html',
  styleUrls: ['./add-user.component.css']
})
export class AddUserComponent implements OnInit {


  @Output('adduser') hideAdduser = new EventEmitter();


  login: any = "";

  //loading
  loading = false;

  countryList: CountryListResponse[] = [];
  roleSetting: MultiSelectDropdownSettings;
  countrySetting: MultiSelectDropdownSettings;

  userRolesSelection: string[] = [];

  //Text box max limit set
  textBoxMaxLength: number = TextBoxMaxLength;
  textBoxMaxCharactersAllowedMessage: string = TextBoxMaxCharactersAllowedMessage;


  constructor(
    private userApiCallService: UserApiCallService,
    private exceptionService: ExceptionHandlingService,
    private toste: ToastrService,
    private roleApiCallService: RoleApiCallService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private countryCacheService: CountryCacheService,
    private commonsService: CommonsService
  ) { }

  ngOnInit() {
    this.roleSetting = this.multiSelectDropDownSettingService.getRoleDrpSetting(true);
    this.countrySetting = this.multiSelectDropDownSettingService.getCountryDrpSetting(false, true);
    this.getInitData();
  }

  /**
   * Get RoleName List 
   * <AUTHOR>
   */
  public async getInitData(): Promise<void> {
    this.loading = true;
    this.userRolesSelection = await this.roleApiCallService.getRoleNameList();
    this.countryList = await this.countryCacheService.getCountryListFromCache();
    this.loading = false;
  }

  form = new FormGroup({
    firstName: new FormControl('', [Validators.required, Validators.pattern('[a-zA-Z ]*'), Validators.maxLength(this.textBoxMaxLength)]),
    lastName: new FormControl('', [Validators.required, Validators.pattern('[a-zA-Z ]*'), Validators.maxLength(this.textBoxMaxLength)]),
    email: new FormControl('', [Validators.required, Validators.pattern(EMAIL_VALIDATION_PATTERN), Validators.maxLength(this.textBoxMaxLength)]),
    userRoles: new FormControl([], Validators.required),
    country: new FormControl([], Validators.required)
  });

  public onItemSelectValidation(fieldName: string) {
    if (this.form.invalid) {
      this.form.get(fieldName).markAsTouched();
    }
  }

  public createAccount(): void {
    this.loading = true;
    let formData = this.form.value;
    let member = new Member(formData.email.split("@")[0],
      formData.firstName,
      formData.lastName,
      formData.email,
      formData.userRoles,
      this.commonsService.getIdsFromArray(formData.country));
    this.userApiCallService.saveMember(member).subscribe({
      next: (response: HttpResponse<UserResponse>) => {
        if (response.status == 200) {
          this.loading = false;
          this.hideAdduser.emit();
          this.toste.success("Succesfully added.");
        }
      },
      error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });

  }

  public change(event: any): void {
    if (event.target.value.search("@") < 0) {
      this.login = event.target.value;
    }
    if (event.target.value.search("@") > 0) {
      let finallogin = event.target.value.split("@");
      this.login = finallogin[0];
    }
  }

  public back(): void {
    this.hideAdduser.emit();
  }


}
