import { Location } from '@angular/common';
import { HttpResponse } from '@angular/common/http';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms'; // Ensure FormsModule is imported
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { By } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Router } from '@angular/router';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { API_TIME_OUT, INTERNAL_SERVER_ERROR, ITEMS_PER_PAGE } from '../../app.constants';
import { ConfirmDialogService } from '../../confirmationdialog/confirmation.service';
import { Pageable } from '../../model/common/pageable.model';
import { Sort } from '../../model/common/sort.model';
import { DeviceLogPageResponse } from '../../model/Logs/DeviceLogPageResponse.model';
import { logType } from '../../model/logtype.enum';
import { AuthJwtService } from '../../shared/auth-jwt.service';
import { common_error_empty_end_date, common_error_empty_filter, common_error_empty_start_date, common_error_invalid_date, common_error_other } from '../../shared/config';
import { ExceptionHandlingService } from '../../shared/ExceptionHandling.service';
import { DeviceLogApiCallService } from '../../shared/Service/Device-log/device-log-api-call.service';
import { MessageService } from '../../shared/Message.service';
import { PermissionService } from '../../shared/permission.service';
import { CommonsService } from '../../shared/util/commons.service';
import { commonsProviders, testDropdownInteraction } from '../../Tesing-Helper/test-utils';
import { DeviceLogComponent } from './device-log.component';
import { FileSize } from '../../shared/enum/Device-log/fileSize.pipe';
import { DeviceDetailComponent } from '../../device-detail/device-detail.component';
import { HidePermissionNamePipe } from '../../shared/pipes/Role/hidePermissionName.pipe';
import { RoleApiCallService } from '../../shared/Service/RoleService/role-api-call.service';
import { CommonOperationsService } from '../../shared/util/common-operations.service';
import { KeyValueMappingServiceService } from '../../shared/util/key-value-mapping-service.service';
import { PrintListPipe } from '../../shared/pipes/printList.pipe';
import { EnumMappingDisplayNamePipe } from '../../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';

describe('DeviceLogComponent', () => {
  let component: DeviceLogComponent;
  let fixture: ComponentFixture<DeviceLogComponent>;
  let router: Router;
  let location: Location;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let authServiceMock: jasmine.SpyObj<AuthJwtService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let permissionsServiceMock: jasmine.SpyObj<PermissionService>;
  let logDetailServiceMock: jasmine.SpyObj<DeviceLogApiCallService>;
  let exceptionHandlingService: ExceptionHandlingService;
  let logtypeResponse = [{
    "key": "DIAGNOSIS",
    "value": "DIAGNOSIS"
  }, {
    "key": "CRASH",
    "value": "CRASH"
  }, {
    "key": "US2DIAGNOSIS",
    "value": "US2DIAGNOSIS"
  }, {
    "key": "MANUFACTURING",
    "value": "MANUFACTURING"
  }]
  let logTypeListingResponse = {
    "content": [{
      "deviceIdPk": 123,
      "deviceId": "3",
      "serialNumber": "HUI-1259-1",
      "deviceStatus": null,
      "logType": logType.MANUFACTURING,
      "examId": null,
      "createdDate": 1728632729000,
      "uploadedDate": 1728632735125,
      "fileSize": 3879,
      "fileName": "HUI-1259-1/logs/manufacturing/2024/10/11/echonousManufacturing_10-11-2024_7-45-29.zip"
    }, {
      "deviceIdPk": 123,
      "deviceId": "5",
      "serialNumber": "HIU-1215-1",
      "deviceStatus": null,
      "logType": logType.MANUFACTURING,
      "examId": null,
      "createdDate": 1727947221000,
      "uploadedDate": 1727947228409,
      "fileSize": 3164,
      "fileName": "HIU-1215-1/logs/manufacturing/2024/10/3/echonousManufacturing_10-3-2024_9-20-21.zip"
    }, {
      "deviceIdPk": 12,
      "deviceId": "6",
      "serialNumber": "HUI-oct-1-3",
      "deviceStatus": null,
      "logType": logType.MANUFACTURING,
      "examId": null,
      "createdDate": 1727771826000,
      "uploadedDate": 1727771832585,
      "fileSize": 3110,
      "fileName": "HUI-oct-1-3/logs/manufacturing/2024/10/1/echonousManufacturing_10-1-2024_8-37-6.zip"
    }, {
      "deviceIdPk": 21,
      "deviceId": "7",
      "serialNumber": "HUI-1206-1",
      "deviceStatus": null,
      "logType": logType.MANUFACTURING,
      "examId": null,
      "createdDate": 1727271935000,
      "uploadedDate": 1727271942132,
      "fileSize": 4331,
      "fileName": "HUI-1206-1/logs/manufacturing/2024/9/25/echonousManufacturing_9-25-2024_13-45-35.zip"
    }, {
      "deviceIdPk": 32,
      "deviceId": "8",
      "serialNumber": "sn-mohit-2-9",
      "deviceStatus": null,
      "logType": logType.MANUFACTURING,
      "examId": null,
      "createdDate": 1725353707000,
      "uploadedDate": 1725353716070,
      "fileSize": 3212,
      "fileName": "sn-mohit-2-9/logs/manufacturing/2024/9/3/echonousManufacturing_9-3-2024_8-55-7.zip"
    }, {
      "deviceIdPk": 12,
      "deviceId": "9",
      "serialNumber": "HIU-1162-1",
      "deviceStatus": null,
      "logType": logType.MANUFACTURING,
      "examId": null,
      "createdDate": 1722257693000,
      "uploadedDate": 1722257699550,
      "fileSize": 3135,
      "fileName": "HIU-1162-1/logs/manufacturing/2024/7/29/echonousManufacturing_7-29-2024_12-54-53.zip"
    }, {
      "deviceIdPk": 3,
      "deviceId": null,
      "serialNumber": "HUI-1125-1",
      "deviceStatus": null,
      "logType": logType.MANUFACTURING,
      "examId": null,
      "createdDate": 1718260646000,
      "uploadedDate": 1718260652785,
      "fileSize": 3096,
      "fileName": "HUI-1125-1/logs/manufacturing/2024/6/13/echonousManufacturing_6-13-2024_6-37-26.zip"
    }, {
      "deviceIdPk": 5,
      "deviceId": null,
      "serialNumber": "HUI-jun-12-1",
      "deviceStatus": null,
      "logType": logType.MANUFACTURING,
      "examId": null,
      "createdDate": 1718185066000,
      "uploadedDate": 1718185069442,
      "fileSize": 2985,
      "fileName": "HUI-jun-12-1/logs/manufacturing/2024/6/12/echonousManufacturing_6-12-2024_9-37-46.zip"
    }, {
      "deviceIdPk": 21,
      "deviceId": null,
      "serialNumber": "HUI-1123-1",
      "deviceStatus": null,
      "logType": logType.MANUFACTURING,
      "examId": null,
      "createdDate": 1718179817000,
      "uploadedDate": 1718179823908,
      "fileSize": 3281,
      "fileName": "HUI-1123-1/logs/manufacturing/2024/6/12/echonousManufacturing_6-12-2024_8-10-17.zip"
    }, {
      "deviceIdPk": 5,
      "deviceId": null,
      "serialNumber": "HUI-1096-1",
      "deviceStatus": null,
      "logType": logType.MANUFACTURING,
      "examId": null,
      "createdDate": 1718097063000,
      "uploadedDate": 1718097070190,
      "fileSize": 3096,
      "fileName": "HUI-1096-1/logs/manufacturing/2024/6/11/echonousManufacturing_6-11-2024_9-11-3.zip"
    }],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 10,
      "sort": {
        "empty": true,
        "sorted": false,
        "unsorted": true
      },
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "last": false,
    "totalPages": 11,
    "totalElements": 101,
    "size": 10,
    "number": 0,
    "sort": {
      "empty": true,
      "sorted": false,
      "unsorted": true
    },
    "first": true,
    "numberOfElements": 10,
    "empty": false
  }

  beforeEach(async () => {
    // Mock for LocalStorageService
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    localStorageServiceMock.retrieve.and.returnValue('mock-token'); // Set a mock return value

    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    authServiceMock = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    permissionsServiceMock = jasmine.createSpyObj('PermissionsService', ['getDevicePermission', 'getDeviceLogPermission']);
    logDetailServiceMock = jasmine.createSpyObj('LogDetailService', ['getLogTypes', 'getLogDetail']);
    exceptionHandlingService = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);

    await TestBed.configureTestingModule({
      declarations: [DeviceLogComponent, FileSize, DeviceDetailComponent, HidePermissionNamePipe],
      imports: [FormsModule, // Include FormsModule for ngModel support
        NgbPaginationModule,
        MatFormFieldModule,
        MatInputModule,
        MatDatepickerModule,
        MatNativeDateModule,
        BrowserAnimationsModule,
        NgMultiSelectDropDownModule.forRoot(),
        ReactiveFormsModule],
      providers: [
        AuthJwtService,
        { provide: LocalStorageService, useValue: localStorageServiceMock }, // Provide the mock service
        SessionStorageService,
        RoleApiCallService,
        CommonOperationsService,
        HidePermissionNamePipe,
        KeyValueMappingServiceService,
        PrintListPipe,
        EnumMappingDisplayNamePipe,
        CommonsService,
        ConfirmDialogService,
        MessageService,
        { provide: DeviceLogApiCallService, useValue: logDetailServiceMock },
        { provide: PermissionService, useValue: permissionsServiceMock },
        { provide: AuthJwtService, useValue: authServiceMock },
        { provide: common_error_empty_filter, useValue: 'Some default value' },
        { provide: common_error_empty_start_date, useValue: 'Your mock error message' },
        { provide: common_error_empty_end_date, useValue: 'Your mock error message for end date' },
        { provide: common_error_invalid_date, useValue: 'Invalid date error' },
        { provide: common_error_other, useValue: 'Mock error message' },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeviceLogComponent);
    router = TestBed.inject(Router);
    location = TestBed.inject(Location);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService)
    component = fixture.componentInstance;
    fixture.detectChanges();
    // Initialize Router navigation
    router.initialNavigation();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('when unauthenticat user try to access component', async () => {
    // `loginNavigate` is already spied on in the beforeEach setup, so no need to call `spyOn` here
    authServiceMock.isAuthenticate.and.returnValue(false);

    component.ngOnInit();

    // Use async check to wait for navigation to complete
    await fixture.whenStable();

    // Expect `loginNavigate` to have been called
    expect(authServiceMock.loginNavigate).toHaveBeenCalled();

    // Assert that the path has changed to the login page (empty path in this case)
    expect(location.path()).toBe('');
  });

  it('should initialize form controls on ngOnInit', () => {
    // Arrange: Set up the mocks for dependencies and expected service responses
    authServiceMock.isAuthenticate.and.returnValue(true);
    permissionsServiceMock.getDevicePermission.and.returnValue(true); // Mocking device permissions
    permissionsServiceMock.getDeviceLogPermission.and.returnValue(true);    // Mocking job permissions
    logDetailServiceMock.getLogTypes.and.returnValue(of(new HttpResponse({
      body: logtypeResponse, // Mocked package response
      status: 200,
      statusText: 'OK',
    })));
    logDetailServiceMock.getLogDetail.and.returnValue(of(new HttpResponse<DeviceLogPageResponse>({
      body: logTypeListingResponse, // Mocked package response
      status: 200,
      statusText: 'OK',
    })));
    component.ngOnInit();
    // Assert: Verify permissions were correctly set based on service responses
    expect(component.downloadDeviceLogPermission).toBeTruthy();
    expect(component.deviceReaderPermission).toBeTruthy();

    // Assert: Verify component state and pagination defaults
    expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE); // Ensure pagination is set correctly
    expect(component.drpselectsize).toBe(ITEMS_PER_PAGE);
    expect(component.previousPage).toBe(1);

    expect(component.dataSizes).toEqual(['10', '25', '50', '100']);
    // Assert: Confirm default display states for job details and device detail sections
    expect(component.displaylog).toBeTruthy();
    expect(component.displayDeviceDetail).toBeFalsy();

    expect(component.logTypeList).toEqual(logtypeResponse);
    expect(component.filterForm.get('logType').value).toBeNull();
    expect(component.filterForm.get('logDeviceId').value).toBeNull();
    expect(component.filterForm.get('logSerialNumber').value).toBeNull();
    expect(component.filterForm.get('logExamId').value).toBeNull();
    expect(component.filterForm.get('logFrom').value).toBeNull();
    expect(component.filterForm.get('logTo').value).toBeNull();

    expect(component.totalItems).toEqual(logTypeListingResponse.totalElements);
    expect(component.logsDetail).toEqual(logTypeListingResponse.content);
    expect(component.page).toEqual(logTypeListingResponse.number + 1);
    expect(component.totalLog).toEqual(logTypeListingResponse.totalElements);
    expect(component.totalLogDisplay).toEqual(logTypeListingResponse.numberOfElements);



  });

  it('should call toastrService.error with INTERNAL_SERVER_ERROR', () => {
    // Arrange: Set up authentication mock and simulate an error response for getLogTypes
    authServiceMock.isAuthenticate.and.returnValue(true);
    logDetailServiceMock.getLogTypes.and.returnValue(
      throwError(() => ({
        status: 500, // HTTP status code indicating a server error
        statusText: 'Internal Server Error',
        message: 'Server error'
      }))
    );

    logDetailServiceMock.getLogDetail.and.returnValue(
      throwError(() => ({
        status: 504, // HTTP status code indicating a server error
        statusText: 'Internal Server Error',
        message: 'Server error'
      }))
    );


    // Spy on the customErrorMessage method in exceptionHandlingService to verify error handling
    spyOn(exceptionHandlingService, 'customErrorMessage').and.callThrough();

    // Act: Trigger the component's initialization to invoke getJobList and error handling
    component.ngOnInit();

    // Assert: Ensure customErrorMessage was called for error handling
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();

    // Assert: Confirm toastrService.error was called with an INTERNAL_SERVER_ERROR message
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    // Assert: Confirm toastrService.error was called with an API_TIME_OUT message
    expect(toastrServiceMock.error).toHaveBeenCalledWith(API_TIME_OUT);
    expect(component.loading).toBeFalsy();
  });


  it('LoadAll method gives error', () => {
    // Arrange: Set up the mock authentication and simulate an error response from the getLogDetail service
    authServiceMock.isAuthenticate.and.returnValue(true);
    logDetailServiceMock.getLogDetail.and.returnValue(of(new HttpResponse<DeviceLogPageResponse>({
      body: {
        content: [],
        pageable: new Pageable(null, 0, 0, 0, false, false),
        totalPages: 0,
        last: false,
        totalElements: 0,
        numberOfElements: 0,
        first: false,
        sort: new Sort(false, false, false),
        size: 0,
        number: 0,
        empty: false
      },
      status: 500, // Mock HTTP status indicating a server error
      statusText: 'OK',
    })));

    // Act: Initialize component to trigger the loadAll method which calls getLogDetail
    component.ngOnInit();

    // Assert: Verify that no LogDetails are loaded when an error response is received
    expect(component.logsDetail.length).toEqual(0); // LogDetails  should be loaded
    expect(component.totalLogDisplay).toEqual(0); // Total totalLogDisplay count should be 0
    expect(component.totalLog).toEqual(0); // Display totalLog should be 0
    expect(component.loading).toBeFalse(); // Loading indicator should be false as loading is complete
  });

  it('should display the correct options in the dropdown change', fakeAsync(() => {

    // Mock the authentication service to return true, simulating an authenticated user
    authServiceMock.isAuthenticate.and.returnValue(true);

    // Trigger the component's ngOnInit lifecycle hook to initialize the component
    component.ngOnInit();

    // Act: Simulate asynchronous operations and update the view with any necessary data changes
    tick(); // Simulates the passage of time required for asynchronous operations (if any)
    fixture.detectChanges(); // Run change detection to ensure the view is updated with the current state

    // Test dropdown interaction
    testDropdownInteraction(fixture, component, '#deviceLogShowEntry');
  }));

  it('should call loadPage on pagination page change', async () => {
    // Arrange: Set up authentication and mock the getJobList method with a valid response
    authServiceMock.isAuthenticate.and.returnValue(true);
    spyOn(component, 'loadPage').and.callThrough(); // Spy on the loadPage method to confirm it is called
    logDetailServiceMock.getLogDetail.and.returnValue(of(new HttpResponse<DeviceLogPageResponse>({
      body: logTypeListingResponse, // Mocked list of jobs
      status: 200, // HTTP status code for success
      statusText: 'OK',
    })));

    // Act: Initialize the component to populate initial job data
    component.ngOnInit();
    fixture.detectChanges(); // Trigger change detection to apply the initialized state

    // Await for asynchronous actions to complete
    await fixture.whenStable();

    // Simulate a pagination change to page 2
    const pagination = fixture.debugElement.query(By.css('#deviceLogPagination')); // Locate the pagination element
    pagination.triggerEventHandler('pageChange', 2); // Trigger the page change event

    fixture.detectChanges(); // Apply changes to the DOM after pagination event

    // Assert: Confirm that loadPage method was called with the correct page number
    expect(component.loadPage).toHaveBeenCalledWith(2); // loadPage should have been called for page 2
    expect(component.previousPage).toBe(2); // Ensure previousPage is updated to reflect the current page
  });

  it('should display detail Page', async () => {
    // Mock the authentication service to return true, indicating the user is authenticated
    authServiceMock.isAuthenticate.and.returnValue(true);

    // Mock the permissions service to return true, granting the necessary job access permissions
    permissionsServiceMock.getDevicePermission.and.returnValue(true);

    // Spy on the job service's 'getLogDetail' method and mock the return value to simulate an HTTP response
    logDetailServiceMock.getLogDetail.and.returnValue(of(new HttpResponse<DeviceLogPageResponse>({
      body: logTypeListingResponse, // Mocked list of jobs
      status: 200, // HTTP status code for success
      statusText: 'OK',
    })));
    // Trigger the component's ngOnInit lifecycle hook to initialize and load data
    component.ngOnInit();

    // Run Angular's change detection to reflect any changes in the template after ngOnInit
    fixture.detectChanges();

    // Wait for any asynchronous tasks (like promises or observables) to complete before proceeding
    await fixture.whenStable();

    // Query the DOM for the element with id '#deviceLogToDevieDetail' (the job status element to be clicked)
    const hwIdElement = fixture.debugElement.query(By.css('#deviceLogToDevieDetail'));

    // Ensure that the queried element exists in the DOM before proceeding
    expect(hwIdElement).toBeTruthy();

    // Simulate a click event on the job status element to display job details
    hwIdElement.nativeElement.click();
    // Assert that after the click, the component's 'jobScheduleStatusId' is set correctly
    expect(component.deviceIdInput).toEqual(logTypeListingResponse.content[0].deviceIdPk);

    // Assert that 'displayDeviceDetail' is true, meaning the job detail page is displayed
    expect(component.displayDeviceDetail).toBeTruthy();

    // Assert that 'displaylog' is false, meaning the device detail page is hidden
    expect(component.displaylog).toBeFalsy();

    component.searchLogFilter();

    fixture.detectChanges();

    expect(toastrServiceMock.info).toHaveBeenCalledWith('Some default value');

    component.filterForm.get('logType').setValue("xyz");
    component.filterForm.get('logDeviceId').setValue("xyz");
    component.filterForm.get('logSerialNumber').setValue("xyz");
    component.filterForm.get('logExamId').setValue("xyz");
    component.filterForm.get('logTo').setValue("123456598");

    fixture.detectChanges();

    component.searchLogFilter();

    expect(toastrServiceMock.info).toHaveBeenCalledWith('Your mock error message');

    component.filterForm.get('logFrom').setValue("123456598");
    component.filterForm.get('logTo').setValue(null);

    component.searchLogFilter();

    component.filterForm.get('logFrom').setValue("2025-05-10T12:00:00.000Z");
    component.filterForm.get('logTo').setValue("2025-05-09T12:00:00.000Z");

    component.searchLogFilter();


    component.filterForm.get('logFrom').setValue("2025-05-09T12:00:00.000Z");
    component.filterForm.get('logTo').setValue("2025-05-10T12:00:00.000Z");

    component.searchLogFilter();
  });

});
