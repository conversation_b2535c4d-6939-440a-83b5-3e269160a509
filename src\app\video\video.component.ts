import { HttpBackend, HttpClient, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { isNullOrUndefined } from 'is-what';
import * as J<PERSON>Zip from 'jszip';
import { ToastrService } from 'ngx-toastr';
import { DownloadVideoFileError, ITEMS_PER_PAGE, Medium_TextBoxMaxCharactersAllowedMessage, Medium_TextBoxMaxLength, SPECIAL_CHARACTER_PATTERN, SpecialCharacterErrorMessage } from '../app.constants';
import { SuccessMessageResponse } from '../model/common/SuccessMessageResponse.model';
import { DownloadVideoResponse } from '../model/video/download-video-response.model';
import { VideoListResponse } from '../model/video/video-list-response.model';
import { VideoSearchRequest } from '../model/video/video-search-request.model';
import { VideoListPagableResponse } from '../model/video/videoListPagableResponse.model';
import { ExceptionHandlingService } from '../shared/ExceptionHandling.service';
import { MessageService } from '../shared/Message.service';
import { AuthJwtService } from '../shared/auth-jwt.service';
import { DeleteVideoJsonConfirmationService } from '../shared/delete-video-json-confirmation.service';
import { PermissionAction } from '../shared/enum/Permission/permissionAction.enum';
import { collapseFilterTextEnum } from '../shared/enum/collapseFilterButtonText.enum';
import { selectColumnHideShowButtonTextEnum } from '../shared/enum/selectColumnHideShowButtonText.enum';
import { PermissionService } from '../shared/permission.service';
import { UploadJsonService } from '../shared/upload-json.service';
import { UploadVideoService } from '../shared/upload-video.service';
import { CommonsService } from '../shared/util/commons.service';
import { VideoService } from '../shared/videoservice/video.service';
import { DownloadService } from '../shared/util/download.service';
import { UserSearchResponse } from '../model/User/UserSearchResponse';
import { BasicModelConfig } from '../model/common/BasicModelConfig.model';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-video',
  templateUrl: './video.component.html',
  styleUrls: ['./video.component.css']
})
export class VideoComponent implements OnInit {

  userDetail: UserSearchResponse[] = [];

  //loading
  loading: boolean = false;

  //permission (add,edit and delete)
  addVideoPermission: boolean = false;
  updateVideoPermission: boolean = false;
  deleteVideoPermission: boolean = false;

  videosList: Array<VideoListResponse> = [];

  //video page
  totalItems: number;
  page: number;
  itemsPerPage: number;
  previousPage: number;

  //drp value set
  drpselectsize = ITEMS_PER_PAGE;

  //message
  totalMemberDisplay = 0;
  totalMember = 0;

  //video id list for delete
  videoIdListcollect: Array<number> = [];


  selectioncheckbox: boolean = false;

  //Id Collect
  localvideoIDListArray: Array<number> = [];

  // Toggle hide/show Video Row selection
  isSelectColumnHidden: boolean = true;
  HideShowSelectColumnButtonText: string = selectColumnHideShowButtonTextEnum.CREATE_JSON_FILE;

  // Toggle hide/show Filter
  isFilterHidden: boolean = false;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;

  //message
  textBoxMaxCharactersAllowedMessage: string = Medium_TextBoxMaxCharactersAllowedMessage;
  specialCharacterErrorMessage: string = SpecialCharacterErrorMessage;
  videoFilterForm = this.formBuilder.group({
    videoTitleJsonTitle: new FormControl('', [Validators.maxLength(Medium_TextBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    videoIdJsonVer: new FormControl('', [Validators.maxLength(Medium_TextBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)])
  });

  // show entry selection
  dataSizes: string[] = [];

  httpClient: HttpClient;

  constructor(
    private formBuilder: FormBuilder,
    private toastrService: ToastrService,
    private messageService: MessageService,
    private permissionService: PermissionService,
    private exceptionService: ExceptionHandlingService,
    private videoService: VideoService,
    private uploadVideoService: UploadVideoService,
    private uploadJsonService: UploadJsonService,
    private deleteVideoJsonConfirmationService: DeleteVideoJsonConfirmationService,
    public handler: HttpBackend,
    private commonsService: CommonsService,
    private authservice: AuthJwtService,
    private downloadService: DownloadService
  ) {
    this.httpClient = new HttpClient(handler);
  }

  ngOnInit() {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.loading = true;
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.setVideoPermission();
      this.dataSizes = this.commonsService.accessDataSizes();
      this.drpselectsize = ITEMS_PER_PAGE;
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.setVideoPermission();
      this.page = 0;
      this.getAllVideosDetail();
    }
  }

  /**
   * set Video Permission
   */
  public setVideoPermission(): void {
    this.addVideoPermission = this.permissionService.getVideoPermission(PermissionAction.ADD_VIDEO_ACTION);
    this.updateVideoPermission = this.permissionService.getVideoPermission(PermissionAction.UPDATE_VIDEO_ACTION);
    this.deleteVideoPermission = this.permissionService.getVideoPermission(PermissionAction.DELETE_VIDEO_ACTION);
  }

  public getAllVideosDetail(): void {

    this.loading = true;
    if (this.videoFilterForm.invalid) {
      this.videoFilterForm.reset();
    }
    let searchRequest = this.setSearchData();
    this.videoService.getVideoList(searchRequest, {
      page: this.page - 1,
      size: this.itemsPerPage
    }
    )?.subscribe({
      next: (response: HttpResponse<VideoListPagableResponse>) => {
        if (!isNullOrUndefined(response) && !isNullOrUndefined(response.body)) {
          this.totalItems = response.body.totalElements;
          this.page = response.body.number + 1;
          this.videosList = response.body.content;

          this.videoIdforDelete(response.body["content"]);

          this.totalMemberDisplay = response.body["numberOfElements"];
          this.totalMember = response.body["totalElements"];
        }
        this.loading = false;
      }, error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  public setSearchData(): VideoSearchRequest {
    let videoTitleJsonTitle = this.commonsService.checkNullFieldValue(this.videoFilterForm.get('videoTitleJsonTitle')?.value);
    let videoIdJsonVer = this.commonsService.checkNullFieldValue(this.videoFilterForm.get('videoIdJsonVer')?.value);
    return new VideoSearchRequest(videoTitleJsonTitle, videoIdJsonVer);
  }

  videoIdforDelete(videoList: any[]): void {
    this.localvideoIDListArray = [];
    for (let index in videoList) {
      this.localvideoIDListArray.push(videoList[index]["parentId"]);
    }
  }

  public searchVideoFilter(): void {
    this.loading = true;
    let searchRequest = this.setSearchData();
    this.videoFilterForm.get("videoTitleJsonTitle").setValue(searchRequest.videoJsonTitle);
    this.videoFilterForm.get("videoIdJsonVer").setValue(searchRequest.videoJsonId);
    if (this.videoFilterForm.invalid || (searchRequest.videoJsonTitle == null && searchRequest.videoJsonId == null)) {
      this.toastrService.info(this.messageService.empty_filter);
      this.loading = false;
    } else {
      this.page = 0;
      this.getAllVideosDetail();
    }
  }

  addOrUpdateJSONFile(jsonFileObj?: any): void {
    if (!isNullOrUndefined(jsonFileObj)) {
      let basicModelConfig: BasicModelConfig = new BasicModelConfig('Update a JSON file', null, 'Update', 'Cancel');
      this.uploadJsonService.confirm(basicModelConfig, jsonFileObj, null, null)
        .then((updateJsonConfirmed) => {
          if (updateJsonConfirmed) {
            this.page = 0;
            this.getAllVideosDetail();
          }
        });
    } else {
      let basicModelConfig: BasicModelConfig = new BasicModelConfig('Create a JSON file', null, 'Create', 'Cancel');
      this.uploadJsonService.confirm(basicModelConfig, null, this.videoIdListcollect, 'Back to Selection')
        .then((createJsonConfirmed) => {
          if (createJsonConfirmed) {
            this.page = 0;
            this.getAllVideosDetail();
            this.videoIdListcollect = [];
            this.hideSelectColumn()
          }
        });
    }
  }

  uploadOrUpdateVideo(videoId: number | null = null, isEditable: boolean = false): void {
    if (!isNullOrUndefined(videoId)) {
      this.uploadVideoService.confirm('Update a Video', null, videoId, isEditable, 'Update', 'Cancel')
        .then((updateVideoConfirmed) => {
          if (updateVideoConfirmed) {
            this.page = 0;
            this.getAllVideosDetail();
          }
        });
    } else {
      this.uploadVideoService.confirm('Upload a Video', null, null, true, 'Upload', 'Cancel')
        .then((uploadVideoConfirmed) => {
          if (uploadVideoConfirmed) {
            this.page = 0;
            this.getAllVideosDetail();
          }
        });
    }
  }

  /**
  * Toggle Filter
  * 
  */
  public toggleFilter(): void {
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
   * Clear all filter
   * 
   * <AUTHOR>
   */
  public clearAllFilter(): void {
    let videoTitleJsonTitle = this.videoFilterForm.get('videoTitleJsonTitle');
    videoTitleJsonTitle.setValue(null);
    let videoIdJsonVer = this.videoFilterForm.get('videoIdJsonVer');
    videoIdJsonVer.setValue(null);
    this.page = 0;
  }

  /**
   * Clear All the filter and Reload all data
   * 
   * <AUTHOR>
   */
  public clearFilter(): void {
    this.videoIdListcollect = [];
    this.clearAllFilter();
    this.getAllVideosDetail();
    this.hideSelectColumn();
  }

  public toggleSelectVideoRows(event: any): void {
    let id = event.target.id;
    if (id == selectColumnHideShowButtonTextEnum.CANCEL_JSON_CREATION) {
      this.hideSelectColumn();
    } else if (id == selectColumnHideShowButtonTextEnum.CREATE_JSON_FILE) {
      this.showSelectColumn();
    }
  }

  private hideSelectColumn(): void {
    this.HideShowSelectColumnButtonText = selectColumnHideShowButtonTextEnum.CREATE_JSON_FILE;
    this.isSelectColumnHidden = true;
  }

  private showSelectColumn(): void {
    this.HideShowSelectColumnButtonText = selectColumnHideShowButtonTextEnum.CANCEL_JSON_CREATION;
    this.isSelectColumnHidden = false;
  }

  changeDataSize(datasize: any): void {

    this.loading = true;
    this.videoIdListcollect = [];
    this.itemsPerPage = datasize.target.value;
    this.getAllVideosDetail();
  }

  videoIdList(event: any, id: number): void {
    if (event.target.checked) {
      this.videoIdListcollect.push(id);
    }
    else {
      this.videoIdListcollect.splice((this.videoIdListcollect.indexOf(id)), 1);
    }
  }

  defaultSelectVideo(videoid: number): boolean {

    let index = this.videoIdListcollect.findIndex(id => id == videoid);

    if (index >= 0) {

      return true;
    }
    else {

      return false;
    }

  }

  loadPage(page: number): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.getAllVideosDetail();
    }
  }

  public downloadZipFile(videoId: number, zipFileName: string) {
    let videoUrl: string | null;
    let thumbnailUrl: string | null;
    let subtitlesUrl: string[] = [];
    this.loading = true;
    this.videoService.downloadVideoZipFile(videoId).subscribe({
      next: (downloadVideoResponse: HttpResponse<DownloadVideoResponse>) => {
        if (!isNullOrUndefined(downloadVideoResponse) && !isNullOrUndefined(downloadVideoResponse.body)) {
          videoUrl = downloadVideoResponse.body.videoUrl;
          thumbnailUrl = downloadVideoResponse.body.thumbnailUrl;
          if (downloadVideoResponse.body.subtitles.length > 0) {
            subtitlesUrl = downloadVideoResponse.body.subtitles;
          }
          this.createZipFromFiles(videoUrl, thumbnailUrl, subtitlesUrl, zipFileName);
        } else {
          this.loading = false;
        }
      }, error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  async createZipFromFiles(videoUrl: string, thumbnailUrl: string, subtitlesUrl: string[], zipFileName: string) {
    let zip = new JSZip();

    // video
    const videofileData: any = await this.getFile(videoUrl);
    const videoFileType: any = new Blob([videofileData], { type: '' + videofileData.type + '' });
    zip.file(this.downloadService.decodeFileName(videoUrl), videoFileType);

    // thumbnail
    const thumbnailfileData: any = await this.getFile(thumbnailUrl);
    const thumbnailFileType: any = new Blob([thumbnailfileData], { type: '' + thumbnailfileData.type + '' });
    zip.file(this.downloadService.decodeFileName(thumbnailUrl), thumbnailFileType);

    // srt
    let isSrtBlobObj: boolean = true;
    for (let subtitleObj of subtitlesUrl) {
      const srtfileData: any = await this.getFile(subtitleObj);
      isSrtBlobObj = srtfileData instanceof Blob;
      if (!isSrtBlobObj) {
        break;
      }
      const srtFileType: any = new Blob([srtfileData], { type: '' + srtfileData.type + '' });
      zip.file(this.downloadService.decodeFileName(subtitleObj), srtFileType);
    }
    if (videofileData instanceof Blob && thumbnailfileData instanceof Blob && isSrtBlobObj) {
      let that = this;
      zip.generateAsync({ type: "blob" }).then(function (content) {
        if (content) {
          let downloadLink = document.createElement('a');
          downloadLink.href = window.URL.createObjectURL(new Blob([content], { type: content.type }));
          downloadLink.setAttribute('download', zipFileName);
          document.body.appendChild(downloadLink);
          downloadLink.click();
        }
        that.loading = false;
      });
    } else {
      this.toastrService.error(DownloadVideoFileError);
      this.loading = false;
    }
  }

  async getFile(url: string): Promise<Blob | HttpErrorResponse> {
    const httpOptions = {
      responseType: 'blob' as 'json', // Use 'blob' directly instead of 'json'
      headers: { noAuth: "yes" }
    };
    try {
      const observable = this.httpClient.get<Blob>(url, httpOptions);
      return await firstValueFrom(observable);
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        return error;
      }
      throw error; // Rethrow any other errors
    }
  }

  public downloadJSONFile(jsonId: number, jsonFileName: string) {
    this.loading = true;
    this.videoService.downloadJSONFile(jsonId).subscribe({
      next: (downloadJsonResponse: HttpResponse<any>) => {
        let jsonData = downloadJsonResponse.body["data"];
        const blob = new Blob([JSON.stringify(jsonData)], { type: 'application/json' });
        let downloadLink = document.createElement('a');
        downloadLink.href = window.URL.createObjectURL(new Blob([blob], { type: blob.type }));
        downloadLink.setAttribute('download', jsonFileName);
        document.body.appendChild(downloadLink);
        downloadLink.click();
        this.loading = false;
      }, error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  public deleteVideo(videoId: number): void {
    this.deleteVideoJsonConfirmationService.confirm('Delete Video', 'Are you sure you want to delete video?', 'Delete', 'Cancel')
      .then((confirmed) => {
        if (confirmed) {
          this.loading = true;
          this.videoService.deleteVideo(videoId).subscribe({
            next: (deleteVideoResponse: HttpResponse<SuccessMessageResponse>) => {
              if (!isNullOrUndefined(deleteVideoResponse.body)) {
                this.toastrService.success(deleteVideoResponse.body.message);
              }
              this.getAllVideosDetail();
              this.loading = false;
            },
            error: (error: HttpErrorResponse) => {
              this.loading = false;
              this.exceptionService.customErrorMessage(error);
            }
          });
        }
      }, () => { });
  }

  public deleteJson(jsonId: number): void {
    this.deleteVideoJsonConfirmationService.confirm('Delete Json', 'Are you sure you want to delete json?', 'Delete', 'Cancel')
      .then((confirmed) => {
        if (confirmed) {
          this.loading = true;
          this.videoService.deleteJson(jsonId).subscribe({
            next: (deleteJsonResponse: HttpResponse<SuccessMessageResponse>) => {
              if (!isNullOrUndefined(deleteJsonResponse.body)) {
                this.toastrService.success(deleteJsonResponse.body.message);
              }
              this.getAllVideosDetail();
              this.loading = false;
            },
            error: (error: HttpErrorResponse) => {
              this.loading = false;
              this.exceptionService.customErrorMessage(error);
            }
          });
        }
      }, () => { });
  }
}
