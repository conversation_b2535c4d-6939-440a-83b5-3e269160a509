import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { Cancel, DEVICE_ALREADY_CLIENT, DEVICE_ALREADY_DEMO, DEVICE_ALREADY_TEST, DEVICE_CONVERT_TO_CLIENT, DEVICE_CONVERT_TO_DEMO, DEVICE_CONVERT_TO_TEST, DateTimeDisplayFormat, DeviceDetailResource, DeviceListResource, Device_Select_Message, ITEMS_PER_PAGE, SalesOrderAssociationHeader, Submit } from '../app.constants';
import { ConfirmDialogService } from '../confirmationdialog/confirmation.service';
import { SuccessMessageResponse } from '../model/common/SuccessMessageResponse.model';
import { ListingPageReloadSubjectParameter } from '../model/common/listingPageReloadSubjectParameter.model';
import { CustomerAssociationModelRequest } from '../model/customer-association-model/customer-association-model-request.model';
import { CustomerAssociationRequest } from '../model/customer-association-request';
import { IDevice } from '../model/device.model';
import { DeviceExportCSVSearchRequest } from '../model/device/DeviceExportCSVSearchRequest.model';
import { DeviceSearchRequest } from '../model/device/deviceSearchRequest.model';
import { ExceptionHandlingService } from '../shared/ExceptionHandling.service';
import { DeviceOperationService } from '../shared/Service/DeviceService/device-operation.service';
import { AuthJwtService } from '../shared/auth-jwt.service';
import { DeviceActionService } from '../shared/device-action.service';
import { DeviceService } from '../shared/device.service';
import { ProductStatusEnum } from '../shared/enum/Common/ProductStatus.enum';
import { DeviceListOperations } from '../shared/enum/Operations/DeviceListOperations.enum';
import { PermissionAction } from '../shared/enum/Permission/permissionAction.enum';
import { collapseFilterTextEnum } from '../shared/enum/collapseFilterButtonText.enum';
import { deviceTypesEnum } from '../shared/enum/deviceTypesEnum.enum';
import { CustomerAssociationService } from '../shared/modalservice/customer-association.service';
import { PermissionService } from '../shared/permission.service';
import { CommonOperationsService } from '../shared/util/common-operations.service';
import { CommonsService } from '../shared/util/commons.service';
import { DownloadService } from '../shared/util/download.service';
import { KeyValueMappingServiceService } from '../shared/util/key-value-mapping-service.service';
import { ModuleValidationServiceService } from '../shared/util/module-validation-service.service';
import { DeviceFilterAction } from '../model/device/DeviceFilterAction.model';

@Component({
  selector: 'app-device',
  templateUrl: './device.component.html',
  styleUrls: ['./device.component.css']
})
export class DeviceComponent implements OnInit {

  currentAccount: any;
  devices: IDevice[] = [];
  error: any;
  success: any;
  eventSubscriber: Subscription;
  routeData: any;
  links: any;
  totalItems: any;
  itemsPerPage: any;
  page: number = 0;
  predicate: any;
  previousPage: any;
  reverse: any;
  connectionstate: any;
  deviceLockState: any;
  created: any;
  modified: any;
  lastCheckIn: any;
  msgGeneration: any;

  loading = false;
  form: FormGroup;

  //Device operations
  deviceOperations: string[] = [];
  deviceListResource = DeviceListResource;

  deviceIdList: Array<number> = [];
  localDeviceList: IDevice[];
  selectedDeviceList: IDevice[];
  drpselectsize: number = ITEMS_PER_PAGE;

  // Product status list for template usage
  productStatusList: Array<any> = [];

  checkboxDisplayPermission: boolean = false;

  //Permission Bind
  deviceRederPermission: boolean = false;
  probeReaderPermission: boolean = false;
  jobReaderPermission: boolean = false;
  softwareBuildReaderPermission: boolean = false;
  logReaderPermission: boolean = false;
  userReaderPermission: boolean = false;
  videoReaderPermission: boolean = false;
  roleReaderPermission: boolean = false;
  salesOrderReaderPermission: boolean = false;
  kitManagementReaderPermission: boolean = false;
  countryReaderPermission: boolean = false;
  auditReaderPermission: boolean = false;
  probeConfigGroupPermission: boolean = false;

  deviceIdInput: number;
  displayDevice: boolean = true;
  displayDeviceDetail: boolean = false;

  userrow: FormArray;
  myForm: FormGroup;
  totalDeviceDisplay: number = 0;
  totalDevice: number = 0;
  isFilterHidden: boolean = false;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;

  // show entry selection
  dataSizes: string[] = [];

  //subscription
  subscriptionForCommonloading: Subscription;
  subscriptionForDeviceListFilterRequestParameter: Subscription;

  //Audit Module Hide
  isAuditModuleDisplay: boolean = true;

  //Filter state management properties 
  isFilterComponentInitWithApicall: boolean = true;
  listPageRefreshForbackToDetailPage: boolean = false;
  deviceSearchRequestBody: DeviceSearchRequest = null;

  dateFormat = DateTimeDisplayFormat;

  constructor(
    protected deviceService: DeviceService,
    protected router: Router,
    private fb: FormBuilder,
    private formBuilder: FormBuilder,
    private commonsService: CommonsService,
    private permissionService: PermissionService,
    private toste: ToastrService,
    private exceptionService: ExceptionHandlingService,
    private authservice: AuthJwtService,
    private customerAssociationService: CustomerAssociationService,
    private commonOperationsService: CommonOperationsService,
    private downloadService: DownloadService,
    private moduleValidationServiceService: ModuleValidationServiceService,
    private deviceActionService: DeviceActionService,
    private confirmDialogService: ConfirmDialogService,
    private deviceOperationService: DeviceOperationService,
    private keyValueMappingServiceService: KeyValueMappingServiceService,
  ) {
    this.itemsPerPage = ITEMS_PER_PAGE;
    this.form = this.formBuilder.group({});

    this.myForm = this.fb.group({
      userrow: this.fb.array([])
    });
  }

  ngOnInit() {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.deviceOperations = this.commonOperationsService.accessDeviceListOperations(true, false, this.deviceListResource);
      this.deviceRederPermission = this.permissionService.getDevicePermission(PermissionAction.GET_DEVICE_ACTION);
      let updateDeviceTypePermission = this.permissionService.getDevicePermission(PermissionAction.UPDATE_DEVICE_TYPE_ACTION);
      let lockDevicePermission = this.permissionService.getDevicePermission(PermissionAction.LOCK_DEVICE_ACTION);
      let associateCustomerToDevicePermission = this.permissionService.getDevicePermission(PermissionAction.ASSOCIATE_CUSTOMER_TO_DEVICE_ACTION);
      let disabledPermission = this.permissionService.getDevicePermission(PermissionAction.DISABLE_DEVICE_ACTION);
      let rmaPermission = this.permissionService.getDevicePermission(PermissionAction.RMA_DEVICE_ACTION);
      let editEnableDisablePermission = this.permissionService.getDevicePermission(PermissionAction.EDITABLE_DEVICE_ACTION);

      this.checkboxDisplayPermission = (this.deviceRederPermission || updateDeviceTypePermission || lockDevicePermission || associateCustomerToDevicePermission || disabledPermission || rmaPermission || editEnableDisablePermission);

      // Initialize product status list for template usage
      this.productStatusList = this.keyValueMappingServiceService.enumOptionToList(ProductStatusEnum);

      // Initialize subscription to filter service
      this.subjectInit();

      if (this.deviceRederPermission) {
        this.getDeviceData();
      }
    }
  }

  /**
   * Initialize subject subscriptions
   * <AUTHOR>
   */
  private subjectInit(): void {
    /**
     * This Subject call from Filter component
     * Load all the Data
     * <AUTHOR>
     */
    this.subscriptionForDeviceListFilterRequestParameter =
      this.deviceOperationService
        .getDeviceListFilterRequestParameterSubject()
        ?.subscribe(
          (deviceFilterAction: DeviceFilterAction) => {
            if (deviceFilterAction.listingPageReloadSubjectParameter.isReloadData) {
              if (deviceFilterAction.listingPageReloadSubjectParameter.isDefaultPageNumber) {
                this.deviceIdList = [];
                this.selectedDeviceList = [];
                this.resetPage();
              }
              this.loadAll(deviceFilterAction.deviceSearchRequest);
            }
          }
        );
  }

  /**
   * Loading Status 
   * <AUTHOR>
   */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
  }

  /**
   * Reset Page
   * <AUTHOR>
   */
  private resetPage(): void {
    this.page = 0;
    this.previousPage = 1;
  }

  /**
   * Refresh Filter
   * <AUTHOR>
   */
  public refreshFilter(): void {
    this.resetPage();
    this.filterPageSubjectCallForReloadPage(true, true);
  }

  /**
   * Device page initialization
   * <AUTHOR>
   */
  public getDeviceData(): void {
    this.page = 0;
    this.dataSizes = this.commonsService.accessDataSizes();
    this.drpselectsize = ITEMS_PER_PAGE;
    this.itemsPerPage = ITEMS_PER_PAGE;
    this.isFilterHidden = false;
    this.isFilterComponentInitWithApicall = true;
    this.listPageRefreshForbackToDetailPage = false;
    this.clearDeviceIdCheckBox();
    this.displayDevice = true;
    this.displayDeviceDetail = false;
  }

  /**
   * Update filter cache in background when filter is hidden
   * This ensures cache stays fresh even when filter is not visible
   */
  public updateFilterCacheInBackground(): void {
    // Call the device filter service directly to update cache
    this.deviceOperationService.updateCacheInBackground();
  }

  /**
   * Update only sales order cache when new sales order is added
   * This ensures sales order dropdown has the latest data
   */
  public updateSalesOrderCacheOnly(): void {
    // Call the device filter service to update only sales order cache
    this.deviceOperationService.updateSalesOrderCacheOnly();
  }

  /**
  * Refresh Button Click 
  *
  * <AUTHOR>
  */
  public clickOnRefreshButton(): void {
    this.updateFilterCacheInBackground();
    this.refreshFilter();
  }

  /**
   * Get Device List 
   *
   * <AUTHOR>
   *
   * @param deviceSearchRequest
   */
  public loadAll(deviceSearchRequest: DeviceSearchRequest): void {
    this.deviceSearchRequestBody = deviceSearchRequest;
    let pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage,
    };
    this.setLoadingStatus(true);
    this.deviceService.getDeviceList(deviceSearchRequest, pageObj)?.subscribe({
      next: (res: HttpResponse<any>) => {
        if (res.status == 200) {
          this.paginateDevices(res.body);
          this.setLocalDeviceId(res.body.content);
        } else {
          this.devices = [];
          this.totalDeviceDisplay = 0;
          this.totalDevice = 0;
        }
        this.setLoadingStatus(false);
      },
      error: (error: HttpErrorResponse) => {
        this.setLoadingStatus(false);
        this.exceptionService.customErrorMessage(error);
      },
    });
  }

  protected paginateDevices(data: any) {
    this.totalItems = parseInt(data.totalElements, 10);
    this.page = data.number + 1;
    this.devices = data.content;
    this.totalDeviceDisplay = data.numberOfElements;
    this.totalDevice = data.totalElements;
    this.setLoadingStatus(false);
  }

  setLocalDeviceId(deviceList: IDevice[]) {
    this.localDeviceList = [];
    for (let device of deviceList) {
      this.localDeviceList.push(device);
    }
    this.defaultSelectAll();
  }


  /**
  * Change The Page
  * callDeviceListRefreshSubject ->Call the filter component
  * filter not clear and send with filter request and load data
  * <AUTHOR>
  * @param page
  */
  public loadPage(page): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.clearDeviceIdCheckBox();
      this.filterPageSubjectCallForReloadPage(false, false);
    }
  }



  /**
  * Item par page Value Changes like (10,50,100) 
  * <AUTHOR>
  * @param datasize
  */
  public changeDataSize(datasize): void {
    this.setLoadingStatus(true);
    this.deviceIdList = [];
    this.selectedDeviceList = [];
    this.itemsPerPage = datasize.target.value;
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  public changeDeviceOperation(event: any): void {
    let selectedOperation = event.target.value;
    switch (selectedOperation) {
      case DeviceListOperations.UNLOCK_DEVICES: {
        this.lockUnlock(false);
        break;
      }
      case DeviceListOperations.LOCK_DEVICES: {
        this.lockUnlock(true);
        break;
      }
      case DeviceListOperations.SET_DEVICE_TO_TEST: {
        this.convertDataToTest();
        break;
      }
      case DeviceListOperations.SET_DEVICE_TO_CLIENT: {
        this.convertDataToClient();
        break;
      }
      case DeviceListOperations.SET_DEVICE_TO_DEMO: {
        this.convertDataToDemo();
        break;
      }
      case DeviceListOperations.CUSTOMER_SALES_ORDER_ASSOCIATION: {
        this.associationDeviceWithSalesOrder();
        break;
      }
      case DeviceListOperations.EDIT_ENABLE_DEVICE: {
        this.enableDisableDevice(true);
        break;
      }
      case DeviceListOperations.EDIT_DISABLE_DEVICE: {
        this.enableDisableDevice(false);
        break;
      }
      case DeviceListOperations.Export_CSV: {
        this.exportCSV();
        break;
      }
      case DeviceListOperations.RMA_DEVICES: {
        this.validateProductStatusForRMAAction();
        break;
      }
      case DeviceListOperations.DISABLED_DEVICES: {
        this.validateProductStatusForDisableAction();
        break;
      }
      default:
        break;
    }
    let selection = document.getElementById('deviceOperation') as HTMLSelectElement;
    selection.value = DeviceListOperations.DeviceOperations;
  }


  /**
   * Associaton or Update customer email
   * 
   * <AUTHOR>
   */
  private associationDeviceWithSalesOrder(): void {
    if (this.deviceIdList.length != 0) {
      if (this.validateWithUserInfoAndDeviceInfo()) {
        this.customerAssociationService.openCustomerAssociationPopup(new CustomerAssociationModelRequest(DeviceListResource, SalesOrderAssociationHeader, Submit, Cancel, null))
          .then((confirmedRequestBody: CustomerAssociationRequest) => {
            if (confirmedRequestBody.button) {
              this.setLoadingStatus(true);
              this.deviceService.associationDeviceWithSalesOrder(this.deviceIdList, confirmedRequestBody.basicSalesOrderDetailResponse).subscribe(
                {
                  next: (res: HttpResponse<SuccessMessageResponse>) => {
                    if (confirmedRequestBody.isSalesOrderNewAdd) {
                      this.updateSalesOrderCacheOnly();
                    }
                    this.updateFilterCacheInBackground();
                    this.filterPageSubjectCallForReloadPage(true, false);
                    this.setLoadingStatus(false);
                    this.toste.success(res.body.message);
                  },
                  error: (error: HttpErrorResponse) => {
                    this.setLoadingStatus(false);
                    this.exceptionService.customErrorMessage(error);
                  }
                });
              this.clearDeviceIdCheckBox();
            }
          });
      }
    } else {
      this.toste.info(Device_Select_Message);
    }
  }

  public onChangeDevice(deviceObj: IDevice, event: any): void {
    if (event.target.checked) {
      this.deviceIdList.push(deviceObj.id);
      this.selectedDeviceList.push(deviceObj);
    }
    else {
      let index = this.deviceIdList.findIndex(obj => obj == deviceObj.id);
      this.deviceIdList.splice(index, 1);
      let deviceObjIndex = this.selectedDeviceList.findIndex(obj => obj.id == deviceObj.id);
      this.selectedDeviceList.splice(deviceObjIndex, 1);
    }
    this.defaultSelectAll()
  }

  private convertDataValidation(value: string) {
    let selectedDeviceListFilterByType = this.selectedDeviceList.filter(device => device.deviceType == value);
    if (selectedDeviceListFilterByType.length == this.selectedDeviceList.length) {
      return false;
    }
    return true;
  }

  /**
  * Convert Device Type as Client
  * 
  * <AUTHOR>
  */
  private convertDataToClient(): void {
    if (this.deviceIdList.length != 0) {
      if (this.validateWithUserInfoAndDeviceInfo()) {
        if (this.convertDataValidation(deviceTypesEnum.CLIENT_DEVICE)) {
          this.setLoadingStatus(true);
          this.deviceService.updateDeviceTypeToClient(this.deviceIdList).subscribe({
            next: (res: HttpResponse<any>) => {
              this.responseOfUpdateDeviceType(DEVICE_CONVERT_TO_CLIENT);
            }, error: (error: HttpErrorResponse) => {
              this.setLoadingStatus(false);
              this.exceptionService.customErrorMessage(error);
            }
          });
        } else {
          this.toste.info(DEVICE_ALREADY_CLIENT)

        }
        this.clearDeviceIdCheckBox();
      }
    } else {
      this.toste.info(Device_Select_Message)
    }
  }

  /**
  * Convert Device Type as Demo
  * 
  * <AUTHOR>
  */
  private convertDataToDemo(): void {
    if (this.deviceIdList.length != 0) {
      if (this.validateWithUserInfoAndDeviceInfo()) {
        if (this.convertDataValidation(deviceTypesEnum.DEMO_DEVICE)) {
          this.setLoadingStatus(true);
          this.deviceService.updateDeviceTypeToDemo(this.deviceIdList).subscribe({
            next: (res: HttpResponse<any>) => {
              this.responseOfUpdateDeviceType(DEVICE_CONVERT_TO_DEMO);
            }, error: (error: HttpErrorResponse) => {
              this.setLoadingStatus(false);
              this.exceptionService.customErrorMessage(error);
            }
          });
        } else {
          this.toste.info(DEVICE_ALREADY_DEMO)
        }
        this.clearDeviceIdCheckBox();
      }
    } else {
      this.toste.info(Device_Select_Message)
    }
  }

  private responseOfUpdateDeviceType(message: string) {
    this.filterPageSubjectCallForReloadPage(true, false);
    this.setLoadingStatus(false);
    this.toste.success(message);
  }

  /**
  * Handles API call for locking/unlocking devices
  * 
  * <AUTHOR>
  * @param lockState - Boolean indicating lock (true) or unlock (false) action
  * 
  * Process:
  * 1. Sets loading state during API call
  * 2. Calls device service to update device state
  * 3. On success:
  *    - Shows success message
  *    - Reloads device list
  *    - Clears selection
  * 4. On error:
  *    - Resets loading state
  *    - Shows appropriate error message
  */
  private lockUnlockDeviceApicall(lockState: boolean): void {
    this.setLoadingStatus(true);
    this.deviceService.updateDeviceState(this.deviceIdList, lockState).subscribe({
      next: (res: HttpResponse<SuccessMessageResponse>) => {
        if (res.status == 200) {
          // Successful lock/unlock operation
          this.toste.success(res.body.message);
          this.filterPageSubjectCallForReloadPage(true, false); // Refresh device list
        } else {
          // Handle non-200 responses
          this.setLoadingStatus(false);
        }
        // Clear selection regardless of success
        this.clearDeviceIdCheckBox();
      },
      error: (error: HttpErrorResponse) => {
        // Handle API errors
        this.setLoadingStatus(false);
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
  * Public interface for lock/unlock operations
  * 
  * <AUTHOR>
  * @param lockState - Boolean indicating lock (true) or unlock (false) action
  * 
  * Validates:
  * 1. At least one device selected
  * 2. User has proper permissions and country access
  */
  private lockUnlock(lockState: boolean): void {
    if (this.deviceIdList.length != 0) {
      if (this.validateWithUserInfoAndDeviceInfo()) {
        this.lockUnlockDeviceApicall(lockState);
      }
    } else {
      // Show info when no devices selected
      this.toste.info(Device_Select_Message)
    }
  }

  /**
  * Enables or disables device editing capabilities for selected devices
  * 
  * <AUTHOR>
  * 
  * @param enableState - Boolean flag to enable (true) or disable (false) device editing
  * 
  * Process:
  * 1. Validates selected devices and user's country permissions
  * 2. Calls DeviceActionService to update edit state
  * 3. Reloads device list on success
  * 4. Handles errors and clears selection
  * 5. Shows notification if no devices are selected
  */
  private async enableDisableDevice(enableState: boolean): Promise<void> {
    if (this.deviceIdList.length != 0) {
      if (this.validateUserCountry()) {
        this.setLoadingStatus(true);
        try {
          // Execute device edit state change through service
          await this.deviceActionService.deviceEditAction(this.deviceIdList, enableState)
          // Refresh device list after successful update
          this.filterPageSubjectCallForReloadPage(true, false);
        } catch (error) {
          // Reset loading state on error
          this.setLoadingStatus(false);
        }
      }
      // Clear selection regardless of success/failure
      this.clearDeviceIdCheckBox();
    }
    else {
      // Notify user when no devices are selected
      this.toste.info(Device_Select_Message);
    }
  }

  /**
  * Convert Device Type as Test
  * 
  * <AUTHOR>
  */
  private convertDataToTest(): void {
    if (this.deviceIdList.length != 0) {
      if (this.validateWithUserInfoAndDeviceInfo()) {
        if (this.convertDataValidation(deviceTypesEnum.TEST_DEVICE)) {
          this.setLoadingStatus(true);
          this.deviceService.updateDeviceTypeToTest(this.deviceIdList).subscribe({
            next: (res: HttpResponse<any>) => {
              this.responseOfUpdateDeviceType(DEVICE_CONVERT_TO_TEST);
            }, error: (error: HttpErrorResponse) => {
              this.setLoadingStatus(false);
              this.exceptionService.customErrorMessage(error);
            }
          });
        } else {
          this.toste.info(DEVICE_ALREADY_TEST);
        }
        this.clearDeviceIdCheckBox();
      }
    } else {
      this.toste.info(Device_Select_Message)
    }
  }

  /**
   * Validate Product Status For Disable Action
   * 
   * <AUTHOR>
   */
  private validateProductStatusForDisableAction(): void {
    if (this.deviceIdList.length != 0) {
      if (this.validateWithUserInfoAndDeviceInfo()) {
        this.disableProductStatusForDevice();
      }
    } else {
      this.toste.info(Device_Select_Message)
    }
  }

  /**
  * Validate Product Status For RMA Action
  * 
  * <AUTHOR>
  */
  private validateProductStatusForRMAAction(): void {
    if (this.deviceIdList.length != 0) {
      if (this.validateWithUserInfoAndDeviceInfo()) {
        this.rmaProductStatusForDevice();
      }
    } else {
      this.toste.info(Device_Select_Message)
    }
  }

  /**
  * Disable Product Status For Device
  * 
  * <AUTHOR>
  * @param productStatus 
  */
  private disableProductStatusForDevice(): void {
    let basicModelConfig = this.confirmDialogService.getBasicModelConfigForDisableAction(DeviceDetailResource);
    this.confirmDialogService.confirm(basicModelConfig.title, basicModelConfig.message, basicModelConfig.btnOkText, basicModelConfig.btnCancelText).then((res: boolean) => {
      if (res) {
        this.setLoadingStatus(true);
        this.deviceService.disableProductStatusForDevice(this.deviceIdList).subscribe(
          {
            next: (response: HttpResponse<SuccessMessageResponse>) => {
              this.filterPageSubjectCallForReloadPage(true, false);
              this.toste.success(response.body.message);
              this.clearDeviceIdCheckBox();
            },
            error: (error: HttpErrorResponse) => {
              this.setLoadingStatus(false);
              this.exceptionService.customErrorMessage(error);
            }
          });
      }
    }).finally(() => { })
  }

  /**
   * RMA Product Status For Device
   * 
   * <AUTHOR>
   */
  private rmaProductStatusForDevice(): void {
    let basicModelConfig = this.confirmDialogService.getBasicModelConfigForRMAAction(DeviceDetailResource);
    this.confirmDialogService.confirm(basicModelConfig.title, basicModelConfig.message, basicModelConfig.btnOkText, basicModelConfig.btnCancelText).then((res: boolean) => {
      if (res) {
        this.setLoadingStatus(true);
        this.deviceService.rmaProductStatusForDevice(this.deviceIdList).subscribe(
          {
            next: (response: HttpResponse<SuccessMessageResponse>) => {
              this.filterPageSubjectCallForReloadPage(true, false);
              this.toste.success(response.body.message);
              this.clearDeviceIdCheckBox();
            },
            error: (error: HttpErrorResponse) => {
              this.setLoadingStatus(false);
              this.exceptionService.customErrorMessage(error);
            }
          });
      }
    }).finally(() => { })
  }

  public clearDeviceIdCheckBox(): void {
    this.deviceIdList = [];
    this.selectedDeviceList = [];
    let deviceCheckox = (<HTMLInputElement[]><any>document.getElementsByName("device[]"));
    let deviceLength = deviceCheckox.length;
    for (let index = 0; index < deviceLength; index++) {
      deviceCheckox[index].checked = false;
    }
    let selectDeviceElementId = <HTMLInputElement>document.getElementById("selectDevice");
    if (selectDeviceElementId != null) {
      selectDeviceElementId.checked = false;
    }
  }

  ngOnDestroy() {
    if (this.subscriptionForCommonloading != undefined) {
      this.subscriptionForCommonloading.unsubscribe();
    }
    if (this.subscriptionForDeviceListFilterRequestParameter != undefined) {
      this.subscriptionForDeviceListFilterRequestParameter.unsubscribe();
    }
  }

  public deviceDetailModel(deviceId: number): void {
    this.deviceIdInput = deviceId;
    this.displayDevice = false;
    this.displayDeviceDetail = true;
  }

  /**
  * Show device listing page
  *
  * <AUTHOR>
  */
  public showDevice(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = true;
    this.updateFilterCacheInBackground();
    this.filterPageSubjectCallForReloadPage(false, false);
    this.deviceIdList = [];
    this.displayDevice = true;
    this.displayDeviceDetail = false;
  }

  defaultSelectDevice(deviceId: number): boolean {
    let index = this.deviceIdList.findIndex(id => id == deviceId);
    if (index >= 0) {
      return true;
    }
    else {
      return false;
    }
  }

  defaultSelectAll(): void {
    let res: boolean = false;
    for (let device of this.localDeviceList) {
      let deviceIndex = this.deviceIdList.findIndex(id => id == device.id);
      if (deviceIndex < 0) {
        res = false;
        break;
      }
      else {
        res = true;
      }
    }
    let selectDevice = <HTMLInputElement>document.getElementById("selectDevice");
    if (selectDevice != null) {
      selectDevice.checked = res;
    }
  }

  selectAllDevice(event): void {
    let deviceDataCheckox = (<HTMLInputElement[]><any>document.getElementsByName("device[]"));
    let l = deviceDataCheckox.length;
    if (event.target.checked) {
      for (let i = 0; i < l; i++) {
        deviceDataCheckox[i].checked = true;
      }
      for (let device of this.localDeviceList) {
        let deviceIndex = this.deviceIdList.findIndex(id => id == device.id);
        if (deviceIndex < 0) {
          this.deviceIdList.push(device.id);
          this.selectedDeviceList.push(device);
        }
      }
    }
    else {
      for (let i = 0; i < l; i++) {
        deviceDataCheckox[i].checked = false;
      }
      for (let removeDevice of this.localDeviceList) {
        let deviceIndexRemove = this.deviceIdList.findIndex(id => id == removeDevice.id);
        this.deviceIdList.splice(deviceIndexRemove, 1);
        let deviceObjIndexRemove = this.selectedDeviceList.findIndex(obj => obj.id == removeDevice.id);
        this.selectedDeviceList.splice(deviceObjIndexRemove, 1);
      }
    }
  }

  public exportCSV(): void {
    this.setLoadingStatus(true);
    // Create empty search request for export - filter will be handled by filter component
    let emptySearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    let deviceExportCSVSearchRequest = new DeviceExportCSVSearchRequest(this.deviceIdList, new Date().getTimezoneOffset(), emptySearchRequest);
    // API call to generate the file based on the given device IDs
    this.deviceService.generateCSVFileForDevice(deviceExportCSVSearchRequest).subscribe({
      next: (res: any) => {
        const fileName = res.body.fileName;
        // API call to download the file based on the name recevied from generate file API call
        this.deviceService.downloadCSVFileForDevice(fileName)?.subscribe({
          next: (response: any) => {
            this.downloadService.downloadExportCSV("List_of_Device(s).xls", response);
            this.clearDeviceIdCheckBox();
            this.setLoadingStatus(false);
          }, error: (error) => { this.setLoadingStatus(false); this.exceptionService.customErrorMessage(error); }
        });
      }, error: (error) => {
        this.setLoadingStatus(false);
        this.exceptionService.customErrorMessage(error);
      }
    });

  }

  /**
  * Toggle Filter 
  *
  */
  public toggleFilter(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = false;
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
  * Get Device Associated Country List
  * 
  * @returns 
  */
  public getDeviceAssociatedCountryList(): Array<string> {
    return this.selectedDeviceList.map(device => device.country);
  }

  /**
  * Validation With Device Edit Status 
  * @returns 
  */
  private validateWithUserInfoAndDeviceInfo(): boolean {
    let moduleEditState: Array<boolean> = this.selectedDeviceList.map(device => device.editable);
    return this.moduleValidationServiceService.validateWithEditableWithMultipalRecoard(moduleEditState, DeviceListResource) ? this.validateUserCountry() : false;
  }

  private validateUserCountry() {
    let moduleCountry: Array<string> = this.getDeviceAssociatedCountryList();
    return this.moduleValidationServiceService.validateWithUserCountryForMultileRecord(moduleCountry, DeviceListResource, true);
  }

  /**
   * Call Filter component subject and reload page
   * <AUTHOR>
   * @param isDefaultPageNumber
   * @param isClearFilter
   */
  public filterPageSubjectCallForReloadPage(
    isDefaultPageNumber: boolean,
    isClearFilter: boolean
  ): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false);
    this.deviceOperationService.callRefreshPageSubject(listingPageReloadSubjectParameter, DeviceListResource, this.isFilterHidden, this.deviceSearchRequestBody);
  }
}
