import { Injectable } from '@angular/core';
import { Subject, firstValueFrom } from 'rxjs';
import { isNullOrUndefined } from 'is-what';
import { DeviceSearchRequest } from 'src/app/model/device/deviceSearchRequest.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { DeviceListResource } from 'src/app/app.constants';
import { DeviceService } from '../../device.service';
import { SalesOrderApiCallService } from '../SalesOrderService/sales-order-api-call.service';
import { CountryCacheService } from '../CacheService/countrycache.service';
import { CommonsService } from '../../util/commons.service';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { DeviceFilterAction } from 'src/app/model/device/DeviceFilterAction.model';

/**
* Device Filter Service for communication between device filter and listing components
* Includes caching functionality to avoid unnecessary API calls on filter show/hide
*
* <AUTHOR>
*/
@Injectable({
  providedIn: 'root'
})
export class DeviceOperationService {

  constructor(
    private deviceService: DeviceService,
    private salesOrderApiCallService: SalesOrderApiCallService,
    private countryCacheService: CountryCacheService,
    private commonsService: CommonsService,
    private exceptionHandlingService: ExceptionHandlingService
  ) { }

  /**
  * Cached filter data to avoid API calls on filter show/hide 
  * <AUTHOR>
  */
  private packageVersionList: string[] = [];
  private salesOrderNumberList: string[] = [];
  private countryList: CountryListResponse[] = [];


  //Device list filter
  private deviceListFilterRequestParameterSubject = new Subject<DeviceFilterAction>();

  //Refresh Device List
  private deviceListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  /**
  * Get Device List Filter Request Parameter Subject
  * Used by device listing component to subscribe to filter changes
  * <AUTHOR>
  * @returns Subject<DeviceFilterAction>
  */
  public getDeviceListFilterRequestParameterSubject(): Subject<DeviceFilterAction> {
    return this.deviceListFilterRequestParameterSubject;
  }

  /**
  * Call Device List Filter Request Parameter Subject
  * Used by device filter component to emit filter changes
  * <AUTHOR>
  * @param deviceFilterAction - The filter action containing search parameters
  */
  public callDeviceListFilterRequestParameterSubject(deviceFilterAction: DeviceFilterAction): void {
    this.deviceListFilterRequestParameterSubject.next(deviceFilterAction);
  }

  /**
  * Get Device List Refresh Subject
  * Used by device filter component to subscribe to refresh events
  * <AUTHOR>
  * @returns Subject<ListingPageReloadSubjectParameter>
  */
  public getDeviceListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.deviceListRefreshSubject;
  }

  /**
  * This function call the subject for reload the page data
  * Note : (DeviceListResource) -> Filter page subject call -> Listing page subject call
  * clear all filter after page data Reload
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter
  * @param resourceName
  * @param isFilterHidden
  * @param deviceSearchRequestBodyApply
  */
  public callRefreshPageSubject(
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter,
    resourceName: string,
    isFilterHidden: boolean,
    deviceSearchRequestBodyApply: DeviceSearchRequest
  ): void {
    if (resourceName == DeviceListResource) {
      if (isFilterHidden) {
        let deviceSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
        if (!isNullOrUndefined(deviceSearchRequestBodyApply) && !listingPageReloadSubjectParameter.isClearFilter) {
          deviceSearchRequest = deviceSearchRequestBodyApply;
        }
        let deviceFilterAction = new DeviceFilterAction(listingPageReloadSubjectParameter, deviceSearchRequest);
        this.callDeviceListFilterRequestParameterSubject(deviceFilterAction);
      } else {
        this.deviceListRefreshSubject.next(
          listingPageReloadSubjectParameter
        );
      }
    }
  }

  /**
  * Cache management methods following the sales-order service pattern
  */

  /**
  * Set Package Version List
  * <AUTHOR>
  * @param packageVersionList - Array of package versions to cache
  */
  public setPackageVersionList(packageVersionList: string[]): void {
    this.packageVersionList = packageVersionList;
  }

  /**
  * Get Package Version List
  * <AUTHOR>
  * @returns Cached array of package versions
  */
  public getPackageVersionList(): string[] {
    return this.packageVersionList;
  }

  /**
  * Set Sales Order Number List
  * <AUTHOR>
  * @param salesOrderNumberList - Array of sales order numbers to cache
  */
  public setSalesOrderNumberList(salesOrderNumberList: string[]): void {
    this.salesOrderNumberList = salesOrderNumberList;
  }

  /**
  * Get Sales Order Number List
  * <AUTHOR>
  * @returns Cached array of sales order numbers
  */
  public getSalesOrderNumberList(): string[] {
    return this.salesOrderNumberList;
  }

  /**
  * Set Country List
  * <AUTHOR>
  * @param countryList - Array of countries to cache
  */
  public setCountryList(countryList: CountryListResponse[]): void {
    this.countryList = countryList;
  }

  /**
  * Get Country List
  * <AUTHOR>
  * @returns Cached array of countries
  */
  public getCountryList(): CountryListResponse[] {
    return this.countryList;
  }

  /**
  * Call Device List Refresh Subject
  * Used by device listing component to emit refresh events
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter - The refresh parameters
  */
  public callDeviceListRefreshSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    this.deviceListRefreshSubject.next(listingPageReloadSubjectParameter);
  }

  /**
  * Update cache in background by making fresh API calls
  * This method is called when filter is hidden to keep cache fresh
  * <AUTHOR>
  */
  public async updateCacheInBackground(): Promise<void> {
    // Make API calls in parallel and update cache
    const [packageVersions, salesOrderNumbers, countries] = await Promise.all([
      this.getPackageVersionsFromAPI(),
      this.getSalesOrderNumbersFromAPI(),
      this.getCountriesFromAPI()
    ]);

    // Update cache with fresh data
    this.packageVersionList = packageVersions;
    this.salesOrderNumberList = salesOrderNumbers;
    this.countryList = countries;
  }

  /**
  * Get package versions from API
  * <AUTHOR>
  */
  private async getPackageVersionsFromAPI(): Promise<string[]> {
    const response = await firstValueFrom(this.deviceService.getpackageVersion());
    return this.commonsService.checkForNull(response?.body) || [];
  }

  /**
  * Get sales order numbers from API
  * <AUTHOR>
  */
  private async getSalesOrderNumbersFromAPI(): Promise<string[]> {
    return await this.salesOrderApiCallService.getSalesOrderNumberList();
  }

  /**
  * Get countries from API
  * <AUTHOR>
  */
  private async getCountriesFromAPI(): Promise<CountryListResponse[]> {
    return await this.countryCacheService.getCountryListFromCache(true);
  }

  /**
  * Update only sales order cache when new sales order is added
  * This ensures sales order dropdown has the latest data without calling other APIs
  * <AUTHOR>
  */
  public async updateSalesOrderCacheOnly(): Promise<void> {
    const salesOrderNumbers = await this.getSalesOrderNumbersFromAPI();
    this.salesOrderNumberList = salesOrderNumbers;
  }


}
